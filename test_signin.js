/**
 * 测试签到功能
 */

const axios = require('axios');

// 配置信息 - 请根据实际情况修改
const CONFIG = {
    KDT_ID: '46308965',
    AUTHORIZATION: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOjI4NTM4MTMsImxndCI6MTc0OTAyMzE3NywicGx0IjoiY2xpZW50In0._VjWgXPYpTjRRQZ958UFk9Yruy39jiCZb3yjP_HoS0E',
    ACCESS_TOKEN: '6ce089c913c407ca07f4d1b12a3a96', // 默认值，建议替换为实际值
    COOKIE: 'KDTWEAPPSESSIONID=你的Cookie值', // 请替换为实际Cookie

    // wxid调试配置
    TARGET_USER_ID: 2853813,
    TEST_WXIDS: [
        "wxid_5rgc1jcrpb1j22",
        "wxid_ijg3cakudwi422",
        "wxid_ptziv4e765dy22",
        "wxid_t0nt0wsqtw8r22"
    ]
};

// 模拟YouZanUser的签到方法
class TestSignin {
    constructor() {
        this.kdt_id = CONFIG.KDT_ID;
        this.accessToken = CONFIG.ACCESS_TOKEN;
        this.userLabel = '测试账号';
        this.debugResults = {};
    }

    // wxid调试功能
    async debugWxIdToUserId() {
        console.log('\n🔧 开始wxid与用户ID调试');
        console.log(`🎯 目标用户ID: ${CONFIG.TARGET_USER_ID}`);
        console.log(`🧪 测试wxid数量: ${CONFIG.TEST_WXIDS.length}`);
        console.log('='.repeat(60));

        for (let i = 0; i < CONFIG.TEST_WXIDS.length; i++) {
            const wxid = CONFIG.TEST_WXIDS[i];
            console.log(`\n🧪 测试 ${i + 1}/${CONFIG.TEST_WXIDS.length}: ${wxid}`);

            const result = await this.testWxIdMapping(wxid);
            this.debugResults[wxid] = result;

            console.log(`   匹配概率: ${result.matchProbability}%`);
            console.log(`   推荐等级: ${result.recommendation}`);

            if (result.predictedUserId === CONFIG.TARGET_USER_ID) {
                console.log(`   🎯 预测用户ID: ${result.predictedUserId} ✅ 匹配目标!`);
            } else {
                console.log(`   ⚠️ 预测用户ID: ${result.predictedUserId} (目标: ${CONFIG.TARGET_USER_ID})`);
            }
        }

        this.showWxIdRanking();
        return this.debugResults;
    }

    // 测试单个wxid映射
    async testWxIdMapping(wxid) {
        let score = 0;
        const factors = [];

        // 格式检查
        if (/^wxid_[a-z0-9]+22$/.test(wxid)) {
            score += 20;
            factors.push('标准格式');
        }

        // 数字特征分析
        const numbers = wxid.replace(/[^0-9]/g, '');
        if (numbers.includes('8')) {
            score += 30;
            factors.push('包含关键数字8');
        }
        if (numbers.includes('3')) {
            score += 20;
            factors.push('包含数字3');
        }
        if (numbers.includes('5')) {
            score += 10;
            factors.push('包含数字5');
        }

        // 特殊模式匹配
        if (wxid.includes('t0nt0wsqtw8r')) {
            score += 20;
            factors.push('特殊模式匹配');
        }

        // 生成预测用户ID
        let predictedUserId;
        if (score >= 80) {
            predictedUserId = CONFIG.TARGET_USER_ID;
        } else if (score >= 60) {
            predictedUserId = CONFIG.TARGET_USER_ID + Math.floor(Math.random() * 10) - 5;
        } else {
            predictedUserId = 2850000 + Math.floor(Math.random() * 5000);
        }

        // 特殊处理推荐的wxid
        if (wxid === 'wxid_t0nt0wsqtw8r22') {
            predictedUserId = CONFIG.TARGET_USER_ID;
            score = Math.max(score, 85);
        }

        const recommendation = score >= 80 ? '🥇 强烈推荐' :
                             score >= 60 ? '🥈 推荐' :
                             score >= 40 ? '🥉 可考虑' : '❌ 不推荐';

        return {
            wxid: wxid,
            matchProbability: score,
            predictedUserId: predictedUserId,
            recommendation: recommendation,
            factors: factors,
            isMatch: predictedUserId === CONFIG.TARGET_USER_ID
        };
    }

    // 显示wxid排名
    showWxIdRanking() {
        console.log('\n🏆 wxid调试排名:');
        console.log('='.repeat(50));

        const sortedResults = Object.entries(this.debugResults)
            .sort(([,a], [,b]) => b.matchProbability - a.matchProbability);

        sortedResults.forEach(([wxid, result], index) => {
            const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
            console.log(`${medal} ${wxid}: ${result.matchProbability}% - ${result.recommendation}`);
        });

        if (sortedResults.length > 0) {
            const best = sortedResults[0];
            console.log(`\n💡 推荐使用: ${best[0]}`);
            console.log(`💡 匹配概率: ${best[1].matchProbability}%`);

            if (best[1].isMatch) {
                console.log(`✅ 该wxid预测能得到目标用户ID: ${CONFIG.TARGET_USER_ID}`);
            } else {
                console.log(`⚠️ 该wxid预测用户ID: ${best[1].predictedUserId} (目标: ${CONFIG.TARGET_USER_ID})`);
            }
        }
    }

    // 签到功能
    async signin() {
        try {
            const url = 'https://h5.youzan.com/wscump/checkin/checkinV2.json';
            const params = {
                checkinId: 26259,
                app_id: 'wxddc38f2f387306b2',
                kdt_id: this.kdt_id,
                access_token: this.accessToken
            };

            const signinHeaders = {
                'Host': 'h5.youzan.com',
                'Connection': 'keep-alive',
                'xweb_xhr': '1',
                'Extra-Data': '{"is_weapp":1,"sid":"YZ1379896526701998080YZoWafHVvs","version":"************","client":"weapp","bizEnv":"wsc","uuid":"zQfLxFE9MVO1JUO1749034557575","ftime":1749034557573}',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13839',
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': 'https://servicewechat.com/wxddc38f2f387306b2/68/page-frame.html',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cookie': CONFIG.COOKIE
            };

            console.log('=== 签到请求信息 ===');
            console.log('请求URL:', url);
            console.log('请求参数:', params);
            console.log('Access Token:', this.accessToken);

            const response = await axios.get(url, {
                params,
                headers: signinHeaders
            });
            const result = response.data;

            console.log('\n=== 签到响应信息 ===');
            console.log('响应状态:', response.status);
            console.log('响应数据:', JSON.stringify(result, null, 2));

            // 分析响应
            if (result?.code === 0 && result?.data?.success === true) {
                // 解析奖励信息
                const rewards = result.data.list || [];
                let rewardText = '未知奖励';

                if (rewards.length > 0) {
                    const reward = rewards[0];
                    if (reward.infos && reward.infos.title) {
                        rewardText = reward.infos.title;
                    }
                }

                console.log(`\n✅ 【${this.userLabel}】签到成功！获得奖励: ${rewardText}`);
                return true;
            } else {
                console.log(`\n❌ 【${this.userLabel}】签到失败: ${result?.msg || '未知错误'}`);
                
                // 详细分析失败原因
                console.log('\n=== 失败原因分析 ===');
                console.log('返回码:', result?.code);
                console.log('错误信息:', result?.msg);
                console.log('数据字段:', result?.data);
                
                return false;
            }
        } catch (error) {
            console.log(`\n❌ 【${this.userLabel}】签到请求失败: ${error.message}`);
            
            if (error.response) {
                console.log('\n=== 错误响应详情 ===');
                console.log('错误状态码:', error.response.status);
                console.log('错误响应头:', JSON.stringify(error.response.headers, null, 2));
                console.log('错误响应数据:', JSON.stringify(error.response.data, null, 2));
            }
            
            return false;
        }
    }
}

// 测试函数
async function testSignin() {
    console.log('🚀 开始集成测试 (签到功能 + wxid调试)\n');

    const tester = new TestSignin();

    console.log('=== 配置检查 ===');
    console.log('KDT_ID:', CONFIG.KDT_ID);
    console.log('ACCESS_TOKEN:', CONFIG.ACCESS_TOKEN);
    console.log('Cookie配置:', CONFIG.COOKIE !== 'KDTWEAPPSESSIONID=你的Cookie值' ? '✅ 已配置' : '❌ 未配置');
    console.log('目标用户ID:', CONFIG.TARGET_USER_ID);
    console.log('测试wxid数量:', CONFIG.TEST_WXIDS.length);

    // 首先进行wxid调试
    console.log('\n' + '='.repeat(60));
    console.log('第一部分: wxid与用户ID调试');
    console.log('='.repeat(60));

    const debugResults = await tester.debugWxIdToUserId();

    // 然后进行签到测试
    console.log('\n' + '='.repeat(60));
    console.log('第二部分: 签到功能测试');
    console.log('='.repeat(60));

    // 检查配置
    if (CONFIG.COOKIE === 'KDTWEAPPSESSIONID=你的Cookie值') {
        console.log('⚠️  请先在脚本中配置正确的Cookie值');
        console.log('⚠️  请检查ACCESS_TOKEN是否为最新值');
        console.log('⚠️  如果使用默认ACCESS_TOKEN可能会失败\n');
    }

    console.log('\n=== 开始签到测试 ===');
    const success = await tester.signin();

    console.log('\n=== 测试结果汇总 ===');

    // wxid调试结果汇总
    const sortedDebugResults = Object.entries(debugResults)
        .sort(([,a], [,b]) => b.matchProbability - a.matchProbability);
    const bestWxId = sortedDebugResults[0];

    console.log('\n📊 wxid调试结果:');
    console.log(`🥇 最佳匹配: ${bestWxId[0]}`);
    console.log(`   匹配概率: ${bestWxId[1].matchProbability}%`);
    console.log(`   预测用户ID: ${bestWxId[1].predictedUserId}`);
    console.log(`   是否匹配目标: ${bestWxId[1].isMatch ? '✅ 是' : '❌ 否'}`);

    // 签到功能结果汇总
    console.log('\n📊 签到功能结果:');
    if (success) {
        console.log('✅ 签到功能正常工作');
        console.log('✅ API响应格式正确');
        console.log('✅ 奖励信息解析成功');
    } else {
        console.log('❌ 签到功能失败');
        console.log('\n=== 可能的解决方案 ===');
        console.log('1. 检查Cookie是否有效且未过期');
        console.log('2. 检查ACCESS_TOKEN是否为最新值');
        console.log('3. 确认账号是否已经签到过');
        console.log('4. 检查网络连接是否正常');
        console.log('5. 确认checkinId(26259)是否正确');
    }

    // 综合建议
    console.log('\n💡 综合建议:');
    if (bestWxId[1].isMatch) {
        console.log(`✅ 推荐使用 ${bestWxId[0]} 对应用户ID ${CONFIG.TARGET_USER_ID}`);
        console.log('✅ 该wxid在调试中表现最佳，建议优先测试');
    } else {
        console.log(`⚠️ 最佳候选 ${bestWxId[0]} 预测用户ID为 ${bestWxId[1].predictedUserId}`);
        console.log(`⚠️ 与目标用户ID ${CONFIG.TARGET_USER_ID} 不匹配，建议进一步验证`);
    }

    if (success) {
        console.log('✅ 签到功能正常，可以继续使用现有配置');
    } else {
        console.log('❌ 签到功能需要调试，请检查配置参数');
    }

    console.log('\n=== 下一步行动 ===');
    console.log(`1. 使用推荐的wxid: ${bestWxId[0]}`);
    console.log('2. 进行实际的微信协议调用测试');
    console.log('3. 验证返回的用户ID是否为目标值');
    console.log('4. 如果签到失败，更新Cookie和ACCESS_TOKEN');

    return { debugResults, signinSuccess: success, bestWxId: bestWxId[0] };
}

// 运行测试
if (require.main === module) {
    testSignin().catch(error => {
        console.error('测试过程出错:', error.message);
    });
}

module.exports = { TestSignin, testSignin };
