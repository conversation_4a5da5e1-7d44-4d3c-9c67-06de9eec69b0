#!/usr/bin/env node

/**
 * 有赞小程序API调用工具
 * 基于逆向分析结果开发的实用工具
 */

const crypto = require('crypto');
const https = require('https');
const querystring = require('querystring');

class YouzanApiTool {
  constructor(config = {}) {
    this.config = {
      appId: config.appId || "wx1234567890abcdef",
      pluginId: config.pluginId || "wx9e5eba73bf23a27a",
      version: config.version || "1.0.0",
      debug: config.debug || false,
      
      domains: {
        cashier: "https://cashier.youzan.com",
        uic: "https://uic.youzan.com",
        carmen: "https://open.youzan.com", 
        h5: "https://h5.youzan.com",
        trade: "https://trade.youzan.com",
        qiniu: "https://img.yzcdn.cn",
        money: "https://money.youzan.com"
      }
    };
    
    this.authState = {
      hasToken: false,
      accessToken: "",
      sessionId: "",
      kdtId: "",
      expire: 0
    };
  }

  /**
   * 生成Access Token (模拟算法)
   */
  generateAccessToken(params) {
    const timestamp = Date.now();
    const randomStr = crypto.randomBytes(16).toString('hex');
    const payload = `${params.appId}_${params.openId}_${timestamp}_${randomStr}`;
    const hash = crypto.createHash('sha256').update(payload).digest('hex');
    return `yz_${hash.substring(0, 32)}`;
  }

  /**
   * 生成Session ID (模拟算法)
   */
  generateSessionId(params) {
    const timestamp = Date.now();
    const sessionData = `${params.openId}_${timestamp}`;
    const hash = crypto.createHash('md5').update(sessionData).digest('hex');
    return `sess_${hash}`;
  }

  /**
   * 获取认证Token
   */
  async getAuthToken(params) {
    this.log('开始获取Token', params);
    
    // 验证必需参数
    const required = ['appId', 'openId', 'shopId', 'kdtId'];
    for (const param of required) {
      if (!params[param]) {
        throw new Error(`缺少必需参数: ${param}`);
      }
    }
    
    // 在实际环境中，这里应该调用真实的API
    // 这里使用模拟数据进行演示
    const tokenData = {
      accessToken: this.generateAccessToken(params),
      sessionId: this.generateSessionId(params),
      kdtId: params.kdtId,
      expire: Date.now() + 24 * 3600 * 1000 // 24小时
    };
    
    // 更新认证状态
    this.authState = {
      hasToken: true,
      ...tokenData
    };
    
    this.log('Token获取成功', tokenData);
    return tokenData;
  }

  /**
   * 发起API请求
   */
  async request(options) {
    // 确保有有效Token
    if (!this.authState.hasToken) {
      throw new Error('请先调用getAuthToken()获取认证Token');
    }
    
    // 检查Token是否过期
    if (Date.now() > this.authState.expire) {
      throw new Error('Token已过期，请重新获取');
    }
    
    // 构建请求
    const url = this.buildUrl(options);
    const headers = this.buildHeaders(options.headers);
    
    this.log('发起请求', { url, headers, data: options.data });
    
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: headers
      };
      
      const req = https.request(requestOptions, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            this.log('收到响应', response);
            
            if (response.code === 0) {
              resolve(response.data || response.response);
            } else {
              reject(new Error(response.msg || '请求失败'));
            }
          } catch (error) {
            reject(new Error('响应解析失败'));
          }
        });
      });
      
      req.on('error', reject);
      
      // 发送请求数据
      if (options.data && options.method === 'POST') {
        const postData = typeof options.data === 'string' 
          ? options.data 
          : querystring.stringify(options.data);
        req.write(postData);
      }
      
      req.end();
    });
  }

  /**
   * 构建请求URL
   */
  buildUrl(options) {
    const { origin, path, query = {} } = options;
    const baseUrl = this.config.domains[origin];
    
    if (!baseUrl) {
      throw new Error(`未知的API域名: ${origin}`);
    }
    
    // 自动注入认证参数
    if (!options.noAuth) {
      query.app_id = this.config.appId;
      query.kdt_id = this.authState.kdtId;
      query.access_token = this.authState.accessToken;
      query.store_id = query.store_id || "";
    }
    
    const queryString = querystring.stringify(query);
    return `${baseUrl}${path}${queryString ? '?' + queryString : ''}`;
  }

  /**
   * 构建请求头
   */
  buildHeaders(customHeaders = {}) {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': 'YouzanMiniProgram/1.0.0',
      ...customHeaders
    };
    
    // 添加Extra-Data头
    headers['Extra-Data'] = JSON.stringify({
      is_weapp: 1,
      sid: this.authState.sessionId,
      version: this.config.version
    });
    
    return headers;
  }

  /**
   * 调试日志
   */
  log(action, data) {
    if (this.config.debug) {
      console.log(`[${new Date().toISOString()}] ${action}:`, 
        JSON.stringify(data, null, 2));
    }
  }

  // ==================== 便捷API方法 ====================

  /**
   * 获取地址列表
   */
  async getAddressList() {
    return this.request({
      origin: 'cashier',
      method: 'POST',
      path: '/wsctrade/uic/address/getAddressList.json'
    });
  }

  /**
   * 获取商品详情
   */
  async getGoodsDetail(goodsId) {
    return this.request({
      origin: 'h5',
      method: 'GET',
      path: '/wscgoods/detail/getGoodsInfo.json',
      query: { goods_id: goodsId }
    });
  }

  /**
   * 添加商品到购物车
   */
  async addToCart(goodsId, skuId, quantity = 1) {
    return this.request({
      origin: 'trade',
      method: 'POST',
      path: '/wsctrade/cart/add.json',
      data: {
        goods_id: goodsId,
        sku_id: skuId,
        quantity: quantity
      }
    });
  }

  /**
   * 获取购物车列表
   */
  async getCartList() {
    return this.request({
      origin: 'trade',
      method: 'GET',
      path: '/wsctrade/cart/getCartList.json'
    });
  }

  /**
   * 获取订单列表
   */
  async getOrderList(page = 1, pageSize = 20) {
    return this.request({
      origin: 'trade',
      method: 'GET',
      path: '/wsctrade/order/getOrderList.json',
      query: {
        page: page,
        page_size: pageSize
      }
    });
  }

  /**
   * 创建订单
   */
  async createOrder(orderData) {
    return this.request({
      origin: 'trade',
      method: 'POST',
      path: '/wsctrade/order/create.json',
      data: orderData
    });
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    return this.request({
      origin: 'uic',
      method: 'GET',
      path: '/wscuser/account/getUserInfo.json'
    });
  }
}

// 命令行工具
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
有赞小程序API调用工具

使用方法:
  node youzan_api_tool.js <command> [options]

命令:
  auth <appId> <openId> <shopId> <kdtId>  - 获取认证Token
  address                                  - 获取地址列表
  goods <goodsId>                         - 获取商品详情
  cart                                    - 获取购物车
  orders [page] [pageSize]                - 获取订单列表
  user                                    - 获取用户信息

示例:
  node youzan_api_tool.js auth wx123 oUser123 shop123 kdt123
  node youzan_api_tool.js goods 123456
  node youzan_api_tool.js orders 1 10
    `);
    process.exit(0);
  }
  
  const api = new YouzanApiTool({ debug: true });
  const command = args[0];
  
  (async () => {
    try {
      switch (command) {
        case 'auth':
          if (args.length < 5) {
            console.error('用法: auth <appId> <openId> <shopId> <kdtId>');
            process.exit(1);
          }
          const tokenData = await api.getAuthToken({
            appId: args[1],
            openId: args[2], 
            shopId: args[3],
            kdtId: args[4]
          });
          console.log('认证成功:', tokenData);
          break;
          
        case 'address':
          const addresses = await api.getAddressList();
          console.log('地址列表:', addresses);
          break;
          
        case 'goods':
          if (args.length < 2) {
            console.error('用法: goods <goodsId>');
            process.exit(1);
          }
          const goods = await api.getGoodsDetail(args[1]);
          console.log('商品详情:', goods);
          break;
          
        case 'cart':
          const cart = await api.getCartList();
          console.log('购物车:', cart);
          break;
          
        case 'orders':
          const page = parseInt(args[1]) || 1;
          const pageSize = parseInt(args[2]) || 20;
          const orders = await api.getOrderList(page, pageSize);
          console.log('订单列表:', orders);
          break;
          
        case 'user':
          const user = await api.getUserInfo();
          console.log('用户信息:', user);
          break;
          
        default:
          console.error('未知命令:', command);
          process.exit(1);
      }
    } catch (error) {
      console.error('执行失败:', error.message);
      process.exit(1);
    }
  })();
}

module.exports = YouzanApiTool;
