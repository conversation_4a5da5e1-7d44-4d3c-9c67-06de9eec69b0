{"findings": [{"file": "commons.js", "type": "token_function", "content": "function(o,n){var t=u.default.get(\"app:token\");return!n&&t&&\"object\"===e(t)?Promise.resolve(t):function e(o){if(i)return i;return i=new Promise((function(n,t){var r=o=Object.assign({}"}, {"file": "commons.js", "type": "token_function", "content": "function(r){var a=r.code,c=r.data,l=s.a.toCamelCase(c);0==+a?(u.default.set(\"app:token\",l),i=null,n(l),o.success&&o.success(l)):*********==+a?(setTimeout((function(){e(o)}"}, {"file": "commons.js", "type": "token_function", "content": "observer:function(){this.fetchTokenAndSetData()}"}, {"file": "commons.js", "type": "token_function", "content": "attached:function(){this.fetchTokenAndSetData()}"}, {"file": "app.js", "type": "token_function", "content": "function s(){if(e.hasToken()&&(e.getKdtId()||!e.needUpdateKdtIdByServer()))for(var t=null;t=gi.shift();)t.call(null)}"}, {"file": "app.js", "type": "token_function", "content": "function Qi(){return Gi||(Gi=(0,Si.H8)(\"wx.checkSession\")),Gi.then((()=>{Vi(),Gi=null}"}, {"file": "app.js", "type": "token_function", "content": "success:()=>{var e=getApp();e.login((()=>{e.hasToken()&&wx.showToast({title:\"登录成功\"}"}, {"file": "c.js", "type": "token_function", "content": "function M(e){e=s.A.toCamelCase(e),T.hasToken=!0,T.tokenTime=Date.now(),T.accessToken=e.accessToken,T.sessionId=e.sessionId,N(E)}"}, {"file": "c.js", "type": "token_function", "content": "function(e){var t=e.file,r=e.success||n,i=e.fail||n,s=e.progress||n;!function(e){var t=e.success||n,r=e.fail||n;(0,a.rH)({path:\"/wscshop/token/upload-image.json\",success:e=>{t(e.token)}"}, {"file": "c.js", "type": "token_function", "content": "p=(e,t)=>{var o=this.getCurrentAppTokenInfo(a),{status:n,token:i}"}, {"file": "c.js", "type": "token_function", "content": "o=()=>{if(!e.skipToken&&!T.hasToken)return W(E,a,e);a()}"}, {"file": "v.js", "type": "token_function", "content": "function(e){return y({tokenTime:Date.now(),accessToken:e.accessToken,sessionId:e.sessionId}"}, {"file": "v.js", "type": "token_function", "content": "function ft(e){var t=(0,w.gf)();return(0,S.default)({path:\"/wscshop/weapp/enter_shop_after_login.json\",data:{kdt_id:t,ignoreSession:e}"}, {"file": "v.js", "type": "token_function", "content": "546771:(e,t,r)=>{r.r(t),r.d(t,{authorizeHook:()=>a,executeHook:()=>u,executeHookAsync:()=>c,getAppTokenHooks:()=>n.RW,getBeforeYouzanLoginHook:()=>n.g8,getCheckLoginHooks:()=>n.ez,getTeeLoginFailHooks:()=>n.kP,getYouzanLoginFailHooks:()=>n.gR,protocolHook:()=>s,setAppTokenHooks:()=>n.XH,setBeforeYouzanLoginHook:()=>n.km,setCheckLoginHooks:()=>n.MK,setTeeLoginFailHooks:()=>n.qw,setYouzanLoginFailHooks:()=>n.cR}"}, {"file": "v.js", "type": "token_function", "content": "98547:(e,t,r)=>{r.r(t),r.d(t,{afterHooks:()=>I,baymax:()=>z,beforeHooks:()=>w,carmen:()=>Y,default:()=>F,flash:()=>k,getRequestDep:()=>g,requestByCache:()=>K,requestUseCdn:()=>V,requestV2:()=>B,setKdtId:()=>m,setRequestDep:()=>y,updateShop:()=>A,updateToken:()=>T}"}], "authFlows": [{"file": "commons.js", "type": "code处理", "matches": ["coded\"", "coded\"", "code,c=r.data,l=s.a.toCamelCase(c);0==+a?(u.default.set(\"app:token\",l),i=null,n(l),o.success&&o.success(l)):*********==+a?(setTimeout((function(){e(o)"]}, {"file": "commons.js", "type": "openId处理", "matches": ["openId:{type:String,observer:function(){this.fetchTokenAndSetData()", "openId,c=n.appId,l=n.kdtId;if(i&&!this.isFetching){var d=wx.getAccountInfoSync&&wx.getAccountInfoSync(),f=d&&d.miniProgram.appId?d.miniProgram.appId:c;this.isFetching=!0,this.fetchToken({pluginId:\"wx9e5eba7", "openId:i,kdtId:l"]}, {"file": "app.js", "type": "wx.login", "matches": ["wx.login fail\",{error:e=\"object\"==typeof e?e.errMsg:\"微信登录失败\"", "wx.login\").catch((e=>{throw is(\"wx.login fail\",{error:e=\"object\"==typeof e?e.errMsg:\"微信登录失败\""]}, {"file": "app.js", "type": "openId处理", "matches": ["openId:i", "openId:i,has_login:!!t,has_bind:!!n", "openId:s"]}, {"file": "c.js", "type": "openId处理", "matches": ["openId\",\"userId\",\"yzUserId\",\"nickName\",\"fansType\",\"viewTrack2\",\"platformFansId\",\"isDefaultAvatar\"]);r.nickname=r.nickName,delete r.nickName,e.userInfo=r", "openId", "openId:s=\"\",yzUserId:c=\"\",userId:u=\"\",platformFansId:d=\"\",unionId:l=\"\""]}], "algorithms": [{"name": "算法1: 基于微信code + 时间戳", "description": "使用微信登录code、appId、时间戳生成", "code": "\nfunction generateToken(wxCode, appId, kdtId) {\n    const timestamp = Date.now();\n    const data = wxCode + appId + kdtId + timestamp;\n    return btoa(data).replace(/[^a-zA-Z0-9]/g, '').substring(0, 30);\n}\n                "}, {"name": "算法2: 基于openId + 随机数", "description": "使用openId、随机数、固定字符串生成", "code": "\nfunction generateToken(openId, appId) {\n    const random = Math.random().toString(36).substring(2);\n    const data = openId + appId + random;\n    return data.replace(/[^a-f0-9]/g, '').substring(0, 30);\n}\n                "}, {"name": "算法3: 基于用户信息哈希", "description": "使用用户信息进行简单哈希", "code": "\nfunction generateToken(userInfo) {\n    const { openId, appId, kdtId } = userInfo;\n    const timestamp = Math.floor(Date.now() / 1000);\n    let hash = 0;\n    const str = openId + appId + kdtId + timestamp;\n    for (let i = 0; i < str.length; i++) {\n        hash = ((hash << 5) - hash + str.charCodeAt(i)) & 0xffffffff;\n    }\n    return Math.abs(hash).toString(16).padStart(30, '0').substring(0, 30);\n}\n                "}], "timestamp": "2025-06-04T13:12:05.010Z"}