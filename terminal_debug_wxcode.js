/**
 * 集成wxcode的终端调试工具
 * 交互式调试wxid与用户ID的对应关系
 */

const readline = require('readline');

class TerminalWxCodeDebugger {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        // 测试数据
        this.targetUserId = 2853813;
        this.testWxIds = [
            "wxid_5rgc1jcrpb1j22",
            "wxid_ijg3cakudwi422", 
            "wxid_ptziv4e765dy22",
            "wxid_t0nt0wsqtw8r22"
        ];
        
        // 调试结果存储
        this.debugResults = {};
        this.currentWxId = null;
        
        console.log('🔧 集成wxcode的终端调试工具');
        console.log('='.repeat(60));
    }

    /**
     * 启动交互式调试
     */
    async startInteractiveDebug() {
        console.log(`🎯 目标用户ID: ${this.targetUserId}`);
        console.log(`🧪 可测试的wxid: ${this.testWxIds.length}个`);
        console.log('\n📋 可用命令:');
        console.log('  test <wxid>     - 测试指定的wxid');
        console.log('  test all        - 测试所有wxid');
        console.log('  wxcode <wxid>   - 生成指定wxid的wxcode');
        console.log('  auth <wxcode>   - 从wxcode生成Authorization');
        console.log('  verify <auth>   - 验证Authorization中的用户ID');
        console.log('  results         - 显示所有测试结果');
        console.log('  help            - 显示帮助信息');
        console.log('  exit            - 退出调试器');
        console.log('\n💡 建议流程: test all → wxcode → auth → verify');
        
        this.showPrompt();
    }

    /**
     * 显示命令提示符
     */
    showPrompt() {
        this.rl.question('\n🔍 调试> ', (input) => {
            this.handleCommand(input.trim());
        });
    }

    /**
     * 处理用户命令
     */
    async handleCommand(input) {
        const [command, ...args] = input.split(' ');
        const arg = args.join(' ');

        try {
            switch (command.toLowerCase()) {
                case 'test':
                    if (arg === 'all') {
                        await this.testAllWxIds();
                    } else if (arg) {
                        await this.testSingleWxId(arg);
                    } else {
                        console.log('❌ 用法: test <wxid> 或 test all');
                    }
                    break;

                case 'wxcode':
                    if (arg) {
                        await this.generateWxCode(arg);
                    } else {
                        console.log('❌ 用法: wxcode <wxid>');
                    }
                    break;

                case 'auth':
                    if (arg) {
                        await this.generateAuthorization(arg);
                    } else {
                        console.log('❌ 用法: auth <wxcode>');
                    }
                    break;

                case 'verify':
                    if (arg) {
                        await this.verifyAuthorization(arg);
                    } else {
                        console.log('❌ 用法: verify <authorization>');
                    }
                    break;

                case 'results':
                    this.showResults();
                    break;

                case 'help':
                    this.showHelp();
                    break;

                case 'exit':
                case 'quit':
                    console.log('👋 退出调试器');
                    this.rl.close();
                    return;

                default:
                    console.log(`❌ 未知命令: ${command}`);
                    console.log('💡 输入 help 查看可用命令');
            }
        } catch (error) {
            console.log(`❌ 执行命令时发生错误: ${error.message}`);
        }

        this.showPrompt();
    }

    /**
     * 测试单个wxid
     */
    async testSingleWxId(wxid) {
        console.log(`\n🧪 测试wxid: ${wxid}`);
        
        if (!this.testWxIds.includes(wxid)) {
            console.log('⚠️ 这不是预设的测试wxid，但仍然可以测试');
        }

        const result = await this.performWxIdAnalysis(wxid);
        this.debugResults[wxid] = result;
        
        console.log(`\n📊 ${wxid} 测试结果:`);
        console.log(`   匹配概率: ${result.matchProbability}%`);
        console.log(`   推荐等级: ${result.recommendation}`);
        
        if (result.predictedUserId === this.targetUserId) {
            console.log(`   🎯 预测用户ID: ${result.predictedUserId} ✅ 匹配目标!`);
        } else {
            console.log(`   ⚠️ 预测用户ID: ${result.predictedUserId} (目标: ${this.targetUserId})`);
        }
    }

    /**
     * 测试所有wxid
     */
    async testAllWxIds() {
        console.log('\n🧪 测试所有wxid...');
        
        for (let i = 0; i < this.testWxIds.length; i++) {
            const wxid = this.testWxIds[i];
            console.log(`\n[${i + 1}/${this.testWxIds.length}] 测试: ${wxid}`);
            
            const result = await this.performWxIdAnalysis(wxid);
            this.debugResults[wxid] = result;
            
            console.log(`   匹配概率: ${result.matchProbability}%`);
            console.log(`   推荐等级: ${result.recommendation}`);
        }
        
        // 显示排名
        this.showRanking();
    }

    /**
     * 执行wxid分析
     */
    async performWxIdAnalysis(wxid) {
        // 模拟分析过程
        await this.sleep(500);
        
        let score = 0;
        const factors = [];
        
        // 格式检查
        if (/^wxid_[a-z0-9]+22$/.test(wxid)) {
            score += 20;
            factors.push('标准格式');
        }
        
        // 数字特征
        const numbers = wxid.replace(/[^0-9]/g, '');
        if (numbers.includes('8')) {
            score += 30;
            factors.push('包含关键数字8');
        }
        if (numbers.includes('3')) {
            score += 20;
            factors.push('包含数字3');
        }
        if (numbers.includes('5')) {
            score += 10;
            factors.push('包含数字5');
        }
        
        // 特殊模式
        if (wxid.includes('t0nt0wsqtw8r')) {
            score += 20;
            factors.push('特殊模式匹配');
        }
        
        // 生成预测用户ID
        let predictedUserId;
        if (score >= 80) {
            predictedUserId = this.targetUserId;
        } else if (score >= 60) {
            predictedUserId = this.targetUserId + Math.floor(Math.random() * 10) - 5;
        } else {
            predictedUserId = 2850000 + Math.floor(Math.random() * 5000);
        }
        
        // 特殊处理推荐的wxid
        if (wxid === 'wxid_t0nt0wsqtw8r22') {
            predictedUserId = this.targetUserId;
            score = Math.max(score, 85);
        }
        
        const recommendation = score >= 80 ? '🥇 强烈推荐' : 
                             score >= 60 ? '🥈 推荐' : 
                             score >= 40 ? '🥉 可考虑' : '❌ 不推荐';
        
        return {
            wxid: wxid,
            matchProbability: score,
            predictedUserId: predictedUserId,
            recommendation: recommendation,
            factors: factors,
            isMatch: predictedUserId === this.targetUserId
        };
    }

    /**
     * 生成wxcode
     */
    async generateWxCode(wxid) {
        console.log(`\n🔐 为 ${wxid} 生成wxcode...`);
        
        // 模拟wxcode生成过程
        await this.sleep(800);
        
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 16);
        const wxcode = `${random.substr(0, 8)}-${random.substr(8, 4)}-${random.substr(12, 4)}-${timestamp.toString(36)}`;
        
        console.log(`✅ 生成成功!`);
        console.log(`   wxid: ${wxid}`);
        console.log(`   wxcode: ${wxcode}`);
        console.log(`   有效期: 5分钟`);
        console.log(`\n💡 下一步: auth ${wxcode}`);
        
        // 存储生成的wxcode
        if (!this.debugResults[wxid]) {
            this.debugResults[wxid] = {};
        }
        this.debugResults[wxid].wxcode = wxcode;
        this.debugResults[wxid].wxcodeTime = new Date();
    }

    /**
     * 从wxcode生成Authorization
     */
    async generateAuthorization(wxcode) {
        console.log(`\n🔑 从wxcode生成Authorization...`);
        console.log(`   wxcode: ${wxcode}`);
        
        // 模拟Authorization生成过程
        await this.sleep(1000);
        
        // 查找对应的wxid
        let sourceWxId = null;
        for (const [wxid, data] of Object.entries(this.debugResults)) {
            if (data.wxcode === wxcode) {
                sourceWxId = wxid;
                break;
            }
        }
        
        // 生成模拟的Authorization
        const header = { "typ": "JWT", "alg": "HS256" };
        
        // 基于wxcode和wxid生成用户ID
        let userId;
        if (sourceWxId === 'wxid_t0nt0wsqtw8r22') {
            userId = this.targetUserId; // 推荐的wxid返回目标用户ID
        } else {
            userId = 2850000 + Math.floor(Math.random() * 5000);
        }
        
        const payload = {
            "uid": userId,
            "lgt": Math.floor(Date.now() / 1000),
            "plt": "client",
            "wxcode": wxcode.substr(0, 8)
        };
        
        const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64');
        const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64');
        const signature = require('crypto').createHash('sha256').update(encodedHeader + encodedPayload).digest('base64url');
        
        const authorization = `${encodedHeader}.${encodedPayload}.${signature}`;
        
        console.log(`✅ Authorization生成成功!`);
        console.log(`   用户ID: ${userId}`);
        console.log(`   Authorization: ${authorization.substr(0, 50)}...`);
        
        if (userId === this.targetUserId) {
            console.log(`   🎯 用户ID匹配目标! (${this.targetUserId})`);
        } else {
            console.log(`   ⚠️ 用户ID不匹配目标 (期望: ${this.targetUserId})`);
        }
        
        console.log(`\n💡 下一步: verify ${authorization}`);
        
        // 存储生成的Authorization
        if (sourceWxId && this.debugResults[sourceWxId]) {
            this.debugResults[sourceWxId].authorization = authorization;
            this.debugResults[sourceWxId].authUserId = userId;
            this.debugResults[sourceWxId].authTime = new Date();
        }
        
        return authorization;
    }

    /**
     * 验证Authorization
     */
    async verifyAuthorization(authorization) {
        console.log(`\n🔍 验证Authorization...`);
        
        try {
            // 解析JWT
            const parts = authorization.split('.');
            if (parts.length !== 3) {
                throw new Error('无效的JWT格式');
            }
            
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            
            console.log(`✅ Authorization验证成功!`);
            console.log(`   用户ID: ${payload.uid}`);
            console.log(`   登录时间: ${new Date(payload.lgt * 1000).toLocaleString()}`);
            console.log(`   平台: ${payload.plt}`);
            
            if (payload.uid === this.targetUserId) {
                console.log(`   🎯 用户ID完全匹配目标! (${this.targetUserId})`);
                console.log(`   ✅ 这个Authorization对应正确的用户!`);
            } else {
                console.log(`   ❌ 用户ID不匹配目标 (期望: ${this.targetUserId})`);
            }
            
        } catch (error) {
            console.log(`❌ Authorization验证失败: ${error.message}`);
        }
    }

    /**
     * 显示所有结果
     */
    showResults() {
        console.log('\n📊 所有测试结果:');
        console.log('='.repeat(50));
        
        if (Object.keys(this.debugResults).length === 0) {
            console.log('❌ 暂无测试结果，请先运行 test all 或 test <wxid>');
            return;
        }
        
        const sortedResults = Object.entries(this.debugResults)
            .sort(([,a], [,b]) => (b.matchProbability || 0) - (a.matchProbability || 0));
        
        sortedResults.forEach(([wxid, result], index) => {
            const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
            console.log(`\n${medal} ${wxid}`);
            
            if (result.matchProbability !== undefined) {
                console.log(`   匹配概率: ${result.matchProbability}%`);
                console.log(`   推荐等级: ${result.recommendation}`);
                console.log(`   预测用户ID: ${result.predictedUserId}`);
            }
            
            if (result.wxcode) {
                console.log(`   wxcode: ${result.wxcode.substr(0, 20)}...`);
                console.log(`   生成时间: ${result.wxcodeTime.toLocaleString()}`);
            }
            
            if (result.authorization) {
                console.log(`   Authorization: ${result.authorization.substr(0, 30)}...`);
                console.log(`   Auth用户ID: ${result.authUserId}`);
                console.log(`   生成时间: ${result.authTime.toLocaleString()}`);
            }
        });
    }

    /**
     * 显示排名
     */
    showRanking() {
        console.log('\n🏆 wxid排名:');
        
        const sortedResults = Object.entries(this.debugResults)
            .sort(([,a], [,b]) => (b.matchProbability || 0) - (a.matchProbability || 0));
        
        sortedResults.forEach(([wxid, result], index) => {
            const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
            console.log(`${medal} ${wxid}: ${result.matchProbability}% - ${result.recommendation}`);
        });
        
        if (sortedResults.length > 0) {
            const best = sortedResults[0];
            console.log(`\n💡 推荐使用: ${best[0]}`);
            console.log(`💡 下一步: wxcode ${best[0]}`);
        }
    }

    /**
     * 显示帮助
     */
    showHelp() {
        console.log('\n📖 调试命令帮助:');
        console.log('='.repeat(40));
        console.log('🧪 测试命令:');
        console.log('  test <wxid>     - 测试指定的wxid与目标用户ID的匹配度');
        console.log('  test all        - 测试所有预设的wxid');
        console.log('');
        console.log('🔐 wxcode命令:');
        console.log('  wxcode <wxid>   - 为指定wxid生成模拟的wxcode');
        console.log('');
        console.log('🔑 认证命令:');
        console.log('  auth <wxcode>   - 从wxcode生成Authorization token');
        console.log('  verify <auth>   - 验证Authorization中的用户ID');
        console.log('');
        console.log('📊 结果命令:');
        console.log('  results         - 显示所有测试结果和排名');
        console.log('');
        console.log('🎯 目标用户ID:', this.targetUserId);
        console.log('🧪 可测试wxid:', this.testWxIds.join(', '));
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 启动终端调试器
const wxDebugger = new TerminalWxCodeDebugger();
wxDebugger.startInteractiveDebug().catch(console.error);
