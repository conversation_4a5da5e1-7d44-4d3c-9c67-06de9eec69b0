{"testSummary": {"totalTests": 9, "passedTests": 8, "failedTests": 1, "timestamp": "2025-06-04T12:41:58.905Z"}, "testResults": [{"testName": "Token生成", "success": true, "details": {"tokenValid": true, "sessionValid": true, "tokenData": {"accessToken": "yz_7e8264ee2102ed4d511bc75681cc5b9f", "sessionId": "sess_5737b4c033d584c7831a93ab8454d658", "kdtId": "87654321", "expire": 1749127315432}}, "timestamp": "2025-06-04T12:41:55.435Z"}, {"testName": "API请求-获取地址列表", "success": true, "details": {"code": 0, "msg": "success", "data": [{"id": 1, "name": "张三", "phone": "13800138000", "province": "北京市", "city": "北京市", "district": "朝阳区", "detail": "三里屯街道1号"}]}, "timestamp": "2025-06-04T12:41:56.038Z"}, {"testName": "API请求-获取商品详情", "success": true, "details": {"code": 0, "msg": "success", "data": {"goods_id": 123456, "title": "测试商品", "price": 9900, "stock": 100, "images": ["https://img.yzcdn.cn/test.jpg"]}}, "timestamp": "2025-06-04T12:41:56.407Z"}, {"testName": "API请求-添加购物车", "success": true, "details": {"code": 0, "msg": "success", "data": {"cart_id": 1749040916988, "message": "添加成功"}}, "timestamp": "2025-06-04T12:41:56.988Z"}, {"testName": "Token过期检测", "success": false, "details": {"error": "\"undefined\" is not valid JSON"}, "timestamp": "2025-06-04T12:41:56.989Z"}, {"testName": "错误处理-缺少必需参数", "success": true, "details": {"message": "正确捕获错误: 缺少必需参数: openId"}, "timestamp": "2025-06-04T12:41:56.989Z"}, {"testName": "错误处理-无效域名", "success": true, "details": {"message": "正确捕获错误: \"undefined\" is not valid JSON"}, "timestamp": "2025-06-04T12:41:56.990Z"}, {"testName": "错误处理-无Token状态请求", "success": true, "details": {"message": "正确捕获错误: Token无效，需要重新获取"}, "timestamp": "2025-06-04T12:41:56.990Z"}, {"testName": "并发请求", "success": true, "details": {"totalRequests": 5, "successCount": 5, "failureCount": 0}, "timestamp": "2025-06-04T12:41:58.904Z"}], "debugLogs": [{"timestamp": "2025-06-04T12:41:54.705Z", "action": "开始获取Token", "data": {"appId": "wx1234567890abcdef", "openId": "oUser123456789", "shopId": "shop_12345", "kdtId": "87654321", "pluginId": "wx9e5eba73bf23a27a"}}, {"timestamp": "2025-06-04T12:41:55.433Z", "action": "Token生成", "data": {"accessToken": "yz_7e8264ee2102ed4d511bc75681cc5b9f", "sessionId": "sess_5737b4c033d584c7831a93ab8454d658", "kdtId": "87654321", "expire": 1749127315432}}, {"timestamp": "2025-06-04T12:41:55.434Z", "action": "认证状态更新", "data": {"hasToken": true, "hasKdtId": true, "hasShop": true, "tokenTime": 1749040915434, "accessToken": "yz_7e8264ee2102ed4d511bc75681cc5b9f", "sessionId": "sess_5737b4c033d584c7831a93ab8454d658", "kdtId": "87654321", "offlineId": "", "expire": 1749127315432}}, {"timestamp": "2025-06-04T12:41:55.434Z", "action": "Token获取成功", "data": {"accessToken": "yz_7e8264ee2102ed4d511bc75681cc5b9f", "sessionId": "sess_5737b4c033d584c7831a93ab8454d658", "kdtId": "87654321", "expire": 1749127315432}}, {"timestamp": "2025-06-04T12:41:55.435Z", "action": "发起API请求", "data": {"origin": "cashier", "method": "POST", "path": "/wsctrade/uic/address/getAddressList.json"}}, {"timestamp": "2025-06-04T12:41:55.436Z", "action": "构建请求", "data": {"url": "https://cashier.youzan.com/wsctrade/uic/address/getAddressList.json?app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_7e8264ee2102ed4d511bc75681cc5b9f&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_5737b4c033d584c7831a93ab8454d658\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:56.037Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": [{"id": 1, "name": "张三", "phone": "13800138000", "province": "北京市", "city": "北京市", "district": "朝阳区", "detail": "三里屯街道1号"}]}}, {"timestamp": "2025-06-04T12:41:56.038Z", "action": "发起API请求", "data": {"origin": "h5", "method": "GET", "path": "/wscgoods/detail/getGoodsInfo.json", "query": {"goods_id": "123456"}}}, {"timestamp": "2025-06-04T12:41:56.039Z", "action": "构建请求", "data": {"url": "https://h5.youzan.com/wscgoods/detail/getGoodsInfo.json?goods_id=123456&app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_7e8264ee2102ed4d511bc75681cc5b9f&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_5737b4c033d584c7831a93ab8454d658\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:56.406Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": {"goods_id": 123456, "title": "测试商品", "price": 9900, "stock": 100, "images": ["https://img.yzcdn.cn/test.jpg"]}}}, {"timestamp": "2025-06-04T12:41:56.407Z", "action": "发起API请求", "data": {"origin": "trade", "method": "POST", "path": "/wsctrade/cart/add.json", "data": {"goods_id": "123456", "sku_id": "789", "quantity": 1}}}, {"timestamp": "2025-06-04T12:41:56.407Z", "action": "构建请求", "data": {"url": "https://trade.youzan.com/wsctrade/cart/add.json?app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_7e8264ee2102ed4d511bc75681cc5b9f&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_5737b4c033d584c7831a93ab8454d658\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:56.988Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": {"cart_id": 1749040916988, "message": "添加成功"}}}, {"timestamp": "2025-06-04T12:41:56.988Z", "action": "发起API请求", "data": {"origin": "h5", "path": "/test/expired.json"}}, {"timestamp": "2025-06-04T12:41:56.989Z", "action": "开始获取Token", "data": {"appId": "test"}}, {"timestamp": "2025-06-04T12:41:56.989Z", "action": "发起API请求", "data": {"origin": "invalid_domain", "path": "/test.json"}}, {"timestamp": "2025-06-04T12:41:56.990Z", "action": "发起API请求", "data": {"origin": "h5", "path": "/test.json"}}, {"timestamp": "2025-06-04T12:41:56.990Z", "action": "开始获取Token", "data": {"appId": "wx1234567890abcdef", "openId": "oUser123456789", "shopId": "shop_12345", "kdtId": "87654321"}}, {"timestamp": "2025-06-04T12:41:58.244Z", "action": "Token生成", "data": {"accessToken": "yz_ec1519540b7972d2dcf9c8db479ebdad", "sessionId": "sess_0fb2c0823ea3ab8e30cb90aee312e0e4", "kdtId": "87654321", "expire": 1749127318244}}, {"timestamp": "2025-06-04T12:41:58.244Z", "action": "认证状态更新", "data": {"hasToken": true, "hasKdtId": true, "hasShop": true, "tokenTime": 1749040918244, "accessToken": "yz_ec1519540b7972d2dcf9c8db479ebdad", "sessionId": "sess_0fb2c0823ea3ab8e30cb90aee312e0e4", "kdtId": "87654321", "offlineId": "", "expire": 1749127318244}}, {"timestamp": "2025-06-04T12:41:58.245Z", "action": "Token获取成功", "data": {"accessToken": "yz_ec1519540b7972d2dcf9c8db479ebdad", "sessionId": "sess_0fb2c0823ea3ab8e30cb90aee312e0e4", "kdtId": "87654321", "expire": 1749127318244}}, {"timestamp": "2025-06-04T12:41:58.245Z", "action": "发起API请求", "data": {"origin": "h5", "path": "/test/concurrent_0.json", "query": {"request_id": 0}}}, {"timestamp": "2025-06-04T12:41:58.245Z", "action": "构建请求", "data": {"url": "https://h5.youzan.com/test/concurrent_0.json?request_id=0&app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_ec1519540b7972d2dcf9c8db479ebdad&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_0fb2c0823ea3ab8e30cb90aee312e0e4\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:58.246Z", "action": "发起API请求", "data": {"origin": "h5", "path": "/test/concurrent_1.json", "query": {"request_id": 1}}}, {"timestamp": "2025-06-04T12:41:58.246Z", "action": "构建请求", "data": {"url": "https://h5.youzan.com/test/concurrent_1.json?request_id=1&app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_ec1519540b7972d2dcf9c8db479ebdad&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_0fb2c0823ea3ab8e30cb90aee312e0e4\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:58.247Z", "action": "发起API请求", "data": {"origin": "h5", "path": "/test/concurrent_2.json", "query": {"request_id": 2}}}, {"timestamp": "2025-06-04T12:41:58.247Z", "action": "构建请求", "data": {"url": "https://h5.youzan.com/test/concurrent_2.json?request_id=2&app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_ec1519540b7972d2dcf9c8db479ebdad&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_0fb2c0823ea3ab8e30cb90aee312e0e4\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:58.248Z", "action": "发起API请求", "data": {"origin": "h5", "path": "/test/concurrent_3.json", "query": {"request_id": 3}}}, {"timestamp": "2025-06-04T12:41:58.248Z", "action": "构建请求", "data": {"url": "https://h5.youzan.com/test/concurrent_3.json?request_id=3&app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_ec1519540b7972d2dcf9c8db479ebdad&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_0fb2c0823ea3ab8e30cb90aee312e0e4\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:58.248Z", "action": "发起API请求", "data": {"origin": "h5", "path": "/test/concurrent_4.json", "query": {"request_id": 4}}}, {"timestamp": "2025-06-04T12:41:58.248Z", "action": "构建请求", "data": {"url": "https://h5.youzan.com/test/concurrent_4.json?request_id=4&app_id=wx1234567890abcdef&kdt_id=87654321&access_token=yz_ec1519540b7972d2dcf9c8db479ebdad&store_id=", "headers": {"Content-Type": "application/x-www-form-urlencoded", "User-Agent": "YouzanMiniProgram/1.0.0", "Extra-Data": "{\"is_weapp\":1,\"sid\":\"sess_0fb2c0823ea3ab8e30cb90aee312e0e4\",\"version\":\"1.0.0\"}"}}}, {"timestamp": "2025-06-04T12:41:58.505Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": {"timestamp": 1749040918505, "message": "模拟响应数据"}}}, {"timestamp": "2025-06-04T12:41:58.533Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": {"timestamp": 1749040918533, "message": "模拟响应数据"}}}, {"timestamp": "2025-06-04T12:41:58.643Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": {"timestamp": 1749040918643, "message": "模拟响应数据"}}}, {"timestamp": "2025-06-04T12:41:58.720Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": {"timestamp": 1749040918720, "message": "模拟响应数据"}}}, {"timestamp": "2025-06-04T12:41:58.903Z", "action": "收到响应", "data": {"code": 0, "msg": "success", "data": {"timestamp": 1749040918903, "message": "模拟响应数据"}}}], "authState": {"hasToken": true, "hasKdtId": true, "hasShop": true, "tokenTime": 1749040918244, "accessToken": "yz_ec1519540b7972d2dcf9c8db479ebdad", "sessionId": "sess_0fb2c0823ea3ab8e30cb90aee312e0e4", "kdtId": "87654321", "offlineId": "", "expire": 1749127318244}}