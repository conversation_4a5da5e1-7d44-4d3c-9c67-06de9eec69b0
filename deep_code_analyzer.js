/**
 * 深度代码分析器
 * 专门用于分析有赞小程序的token生成算法
 */

const fs = require('fs');
const crypto = require('crypto');

class DeepCodeAnalyzer {
    constructor() {
        this.files = ['c.js', 'v.js', 'app.js', 'commons.js'];
        this.findings = [];
    }

    // 分析单个文件
    analyzeFile(filename) {
        console.log(`\n🔍 分析文件: ${filename}`);
        
        try {
            const content = fs.readFileSync(filename, 'utf8');
            console.log(`   文件大小: ${content.length} 字符`);
            
            // 搜索关键模式
            this.searchTokenPatterns(filename, content);
            this.searchAuthPatterns(filename, content);
            this.searchCryptoPatterns(filename, content);
            this.searchApiPatterns(filename, content);
            
        } catch (error) {
            console.log(`   ❌ 读取失败: ${error.message}`);
        }
    }

    // 搜索Token相关模式
    searchTokenPatterns(filename, content) {
        const patterns = [
            /accessToken[^,;]{0,100}/g,
            /sessionId[^,;]{0,100}/g,
            /hasToken[^,;]{0,100}/g,
            /tokenTime[^,;]{0,100}/g,
            /getToken[^}]{0,200}/g,
            /updateToken[^}]{0,200}/g
        ];

        patterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0) {
                console.log(`   📋 Token模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 80)}...`);
                });
                
                this.findings.push({
                    file: filename,
                    type: 'token',
                    pattern: pattern.toString(),
                    matches: matches.slice(0, 5)
                });
            }
        });
    }

    // 搜索认证相关模式
    searchAuthPatterns(filename, content) {
        const patterns = [
            /getPluginAuthSessionKey[^}]{0,300}/g,
            /login[^}]{0,150}/g,
            /auth[^}]{0,150}/g,
            /wscuser[^"']{0,100}/g,
            /Authorization[^,;]{0,100}/g
        ];

        patterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0) {
                console.log(`   🔐 认证模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 80)}...`);
                });
                
                this.findings.push({
                    file: filename,
                    type: 'auth',
                    pattern: pattern.toString(),
                    matches: matches.slice(0, 3)
                });
            }
        });
    }

    // 搜索加密相关模式
    searchCryptoPatterns(filename, content) {
        const patterns = [
            /md5[^}]{0,100}/gi,
            /sha[^}]{0,100}/gi,
            /encrypt[^}]{0,100}/gi,
            /hash[^}]{0,100}/gi,
            /crypto[^}]{0,100}/gi,
            /sign[^}]{0,100}/gi
        ];

        patterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0) {
                console.log(`   🔒 加密模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 80)}...`);
                });
                
                this.findings.push({
                    file: filename,
                    type: 'crypto',
                    pattern: pattern.toString(),
                    matches: matches.slice(0, 3)
                });
            }
        });
    }

    // 搜索API相关模式
    searchApiPatterns(filename, content) {
        const patterns = [
            /youzan\.com[^"']{0,100}/g,
            /\.json[^"']{0,50}/g,
            /request[^}]{0,150}/g,
            /Extra-Data[^}]{0,100}/g
        ];

        patterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0 && matches.length < 50) { // 避免太多匹配
                console.log(`   🌐 API模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 80)}...`);
                });
                
                this.findings.push({
                    file: filename,
                    type: 'api',
                    pattern: pattern.toString(),
                    matches: matches.slice(0, 5)
                });
            }
        });
    }

    // 分析函数定义
    analyzeFunctions(filename, content) {
        console.log(`\n🔧 分析${filename}中的函数定义...`);
        
        // 搜索函数定义模式
        const functionPatterns = [
            /function\s+\w*[tT]oken\w*[^}]{0,200}/g,
            /function\s+\w*[aA]uth\w*[^}]{0,200}/g,
            /function\s+\w*[lL]ogin\w*[^}]{0,200}/g,
            /\w*[tT]oken\w*\s*[:=]\s*function[^}]{0,200}/g
        ];

        functionPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0) {
                console.log(`   📝 函数模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 100)}...`);
                });
            }
        });
    }

    // 搜索特定的字符串和常量
    searchConstants(filename, content) {
        console.log(`\n📊 分析${filename}中的常量...`);
        
        // 搜索可能的密钥、盐值等
        const constantPatterns = [
            /"[a-f0-9]{16,}"/g,  // 十六进制字符串
            /'[a-f0-9]{16,}'/g,  // 十六进制字符串
            /[0-9]{10,}/g,       // 长数字（可能是时间戳）
            /"wx[a-zA-Z0-9]+"/g, // 微信相关ID
            /"[A-Z]{2,}[0-9]+"/g // 大写字母+数字的模式
        ];

        constantPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0 && matches.length < 20) {
                console.log(`   🔢 常量模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match}`);
                });
            }
        });
    }

    // 运行完整分析
    runFullAnalysis() {
        console.log('🚀 开始深度代码分析\n');
        
        this.files.forEach(filename => {
            if (fs.existsSync(filename)) {
                this.analyzeFile(filename);
                
                // 对大文件进行更详细的分析
                if (filename === 'c.js' || filename === 'v.js') {
                    try {
                        const content = fs.readFileSync(filename, 'utf8');
                        this.analyzeFunctions(filename, content);
                        this.searchConstants(filename, content);
                    } catch (error) {
                        console.log(`   ❌ 详细分析失败: ${error.message}`);
                    }
                }
            } else {
                console.log(`\n❌ 文件不存在: ${filename}`);
            }
        });

        this.generateReport();
    }

    // 生成分析报告
    generateReport() {
        console.log('\n📋 分析报告总结');
        console.log('='.repeat(50));
        
        const typeStats = {};
        this.findings.forEach(finding => {
            typeStats[finding.type] = (typeStats[finding.type] || 0) + 1;
        });
        
        console.log('发现的模式类型:');
        Object.entries(typeStats).forEach(([type, count]) => {
            console.log(`  ${type}: ${count}个模式`);
        });
        
        console.log('\n🎯 关键发现:');
        
        // Token相关发现
        const tokenFindings = this.findings.filter(f => f.type === 'token');
        if (tokenFindings.length > 0) {
            console.log('\n🔑 Token相关:');
            tokenFindings.forEach(finding => {
                console.log(`  文件: ${finding.file}`);
                finding.matches.forEach(match => {
                    console.log(`    - ${match.substring(0, 100)}...`);
                });
            });
        }
        
        // 认证相关发现
        const authFindings = this.findings.filter(f => f.type === 'auth');
        if (authFindings.length > 0) {
            console.log('\n🔐 认证相关:');
            authFindings.forEach(finding => {
                console.log(`  文件: ${finding.file}`);
                finding.matches.forEach(match => {
                    console.log(`    - ${match.substring(0, 100)}...`);
                });
            });
        }
        
        // 加密相关发现
        const cryptoFindings = this.findings.filter(f => f.type === 'crypto');
        if (cryptoFindings.length > 0) {
            console.log('\n🔒 加密相关:');
            cryptoFindings.forEach(finding => {
                console.log(`  文件: ${finding.file}`);
                finding.matches.forEach(match => {
                    console.log(`    - ${match.substring(0, 100)}...`);
                });
            });
        }
        
        console.log('\n💡 下一步建议:');
        console.log('1. 重点分析Token状态管理逻辑');
        console.log('2. 查找微信登录和认证流程');
        console.log('3. 寻找加密算法的具体实现');
        console.log('4. 分析API调用的参数构建过程');
        
        // 保存详细报告
        fs.writeFileSync('code_analysis_report.json', JSON.stringify(this.findings, null, 2));
        console.log('\n📄 详细报告已保存到: code_analysis_report.json');
    }
}

// 运行分析
if (require.main === module) {
    const analyzer = new DeepCodeAnalyzer();
    analyzer.runFullAnalysis();
}

module.exports = DeepCodeAnalyzer;
