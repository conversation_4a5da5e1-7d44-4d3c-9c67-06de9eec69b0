/**
 * Token算法提取器
 * 专门分析有赞小程序的token生成算法
 */

const fs = require('fs');
const crypto = require('crypto');

class TokenAlgorithmExtractor {
    constructor() {
        this.findings = [];
        this.tokenPatterns = [];
        this.authFlows = [];
    }

    // 提取token相关的完整函数
    extractTokenFunctions(filename, content) {
        console.log(`\n🔍 提取${filename}中的Token函数...`);
        
        // 查找包含token的函数定义
        const functionRegex = /function[^{]*{[^}]*(?:token|Token|ACCESS|Session)[^}]*}/g;
        const arrowFunctionRegex = /\w+\s*[:=]\s*\([^)]*\)\s*=>\s*{[^}]*(?:token|Token|ACCESS|Session)[^}]*}/g;
        const objectMethodRegex = /\w+\s*:\s*function[^{]*{[^}]*(?:token|Token|ACCESS|Session)[^}]*}/g;
        
        [functionRegex, arrowFunctionRegex, objectMethodRegex].forEach((regex, index) => {
            const matches = content.match(regex);
            if (matches) {
                console.log(`   📝 函数类型${index + 1}: 找到${matches.length}个`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 100)}...`);
                    this.findings.push({
                        file: filename,
                        type: 'token_function',
                        content: match
                    });
                });
            }
        });
    }

    // 分析微信登录流程
    analyzeWxLoginFlow(filename, content) {
        console.log(`\n🔐 分析${filename}中的微信登录流程...`);
        
        // 查找wx.login相关代码
        const wxLoginRegex = /wx\.login[^}]{0,300}/g;
        const wxCodeRegex = /code[^}]{0,200}/g;
        const openIdRegex = /openId[^}]{0,200}/g;
        
        const patterns = [
            { name: 'wx.login', regex: wxLoginRegex },
            { name: 'code处理', regex: wxCodeRegex },
            { name: 'openId处理', regex: openIdRegex }
        ];
        
        patterns.forEach(pattern => {
            const matches = content.match(pattern.regex);
            if (matches && matches.length < 20) { // 避免太多匹配
                console.log(`   🔑 ${pattern.name}: 找到${matches.length}个匹配`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 120)}...`);
                });
                
                this.authFlows.push({
                    file: filename,
                    type: pattern.name,
                    matches: matches.slice(0, 3)
                });
            }
        });
    }

    // 查找API调用模式
    findApiCallPatterns(filename, content) {
        console.log(`\n🌐 分析${filename}中的API调用模式...`);
        
        // 查找完整的API调用
        const apiCallRegex = /{[^}]*method[^}]*data[^}]*}/g;
        const postRequestRegex = /method\s*:\s*["']post["'][^}]{0,200}/g;
        const dataObjectRegex = /data\s*:\s*{[^}]{0,300}}/g;
        
        [
            { name: 'API调用对象', regex: apiCallRegex },
            { name: 'POST请求', regex: postRequestRegex },
            { name: 'data对象', regex: dataObjectRegex }
        ].forEach(pattern => {
            const matches = content.match(pattern.regex);
            if (matches && matches.length < 10) {
                console.log(`   📡 ${pattern.name}: 找到${matches.length}个`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 150)}...`);
                });
            }
        });
    }

    // 分析token更新逻辑
    analyzeTokenUpdate(filename, content) {
        console.log(`\n🔄 分析${filename}中的Token更新逻辑...`);
        
        // 查找token更新相关的代码
        const updatePatterns = [
            /hasToken\s*=\s*[^;]+/g,
            /tokenTime\s*=\s*[^;]+/g,
            /accessToken\s*=\s*[^;]+/g,
            /sessionId\s*=\s*[^;]+/g,
            /updateToken[^}]{0,200}/g
        ];
        
        updatePatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches) {
                console.log(`   🔄 更新模式${index + 1}: 找到${matches.length}个`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 100)}...`);
                });
            }
        });
    }

    // 查找加密和签名相关代码
    findCryptoAlgorithms(filename, content) {
        console.log(`\n🔒 查找${filename}中的加密算法...`);
        
        // 查找可能的加密函数
        const cryptoPatterns = [
            /btoa\([^)]+\)/g,
            /atob\([^)]+\)/g,
            /encodeURIComponent\([^)]+\)/g,
            /JSON\.stringify\([^)]+\)/g,
            /toString\(16\)/g,
            /Math\.random\(\)[^;]*/g,
            /Date\.now\(\)[^;]*/g,
            /new Date\(\)[^;]*/g
        ];
        
        cryptoPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length < 15) {
                console.log(`   🔐 加密模式${index + 1}: 找到${matches.length}个`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match}`);
                });
            }
        });
    }

    // 提取常量和配置
    extractConstants(filename, content) {
        console.log(`\n📊 提取${filename}中的常量...`);
        
        // 查找可能的配置常量
        const constantPatterns = [
            /"wx[a-zA-Z0-9]+"/g,  // 微信相关ID
            /"[0-9]{8,}"/g,       // 长数字字符串
            /"[a-f0-9]{16,}"/g,   // 十六进制字符串
            /pluginId[^,;]{0,50}/g,
            /appId[^,;]{0,50}/g,
            /kdtId[^,;]{0,50}/g
        ];
        
        constantPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length < 10) {
                console.log(`   📋 常量类型${index + 1}: 找到${matches.length}个`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match}`);
                });
            }
        });
    }

    // 分析具体的token生成逻辑
    analyzeTokenGeneration() {
        console.log('\n🧠 分析Token生成逻辑...');
        
        // 基于发现的模式推断算法
        const tokenFunctions = this.findings.filter(f => f.type === 'token_function');
        
        if (tokenFunctions.length > 0) {
            console.log('🔍 发现的Token相关函数:');
            tokenFunctions.forEach((func, i) => {
                console.log(`   ${i + 1}. ${func.file}: ${func.content.substring(0, 100)}...`);
            });
        }
        
        // 分析认证流程
        if (this.authFlows.length > 0) {
            console.log('\n🔐 认证流程分析:');
            this.authFlows.forEach((flow, i) => {
                console.log(`   ${i + 1}. ${flow.type} (${flow.file}):`);
                flow.matches.forEach(match => {
                    console.log(`      - ${match.substring(0, 80)}...`);
                });
            });
        }
    }

    // 生成可能的算法
    generatePossibleAlgorithms() {
        console.log('\n💡 生成可能的Token算法...');
        
        // 基于分析结果生成算法
        const algorithms = [
            {
                name: '算法1: 基于微信code + 时间戳',
                description: '使用微信登录code、appId、时间戳生成',
                code: `
function generateToken(wxCode, appId, kdtId) {
    const timestamp = Date.now();
    const data = wxCode + appId + kdtId + timestamp;
    return btoa(data).replace(/[^a-zA-Z0-9]/g, '').substring(0, 30);
}
                `
            },
            {
                name: '算法2: 基于openId + 随机数',
                description: '使用openId、随机数、固定字符串生成',
                code: `
function generateToken(openId, appId) {
    const random = Math.random().toString(36).substring(2);
    const data = openId + appId + random;
    return data.replace(/[^a-f0-9]/g, '').substring(0, 30);
}
                `
            },
            {
                name: '算法3: 基于用户信息哈希',
                description: '使用用户信息进行简单哈希',
                code: `
function generateToken(userInfo) {
    const { openId, appId, kdtId } = userInfo;
    const timestamp = Math.floor(Date.now() / 1000);
    let hash = 0;
    const str = openId + appId + kdtId + timestamp;
    for (let i = 0; i < str.length; i++) {
        hash = ((hash << 5) - hash + str.charCodeAt(i)) & 0xffffffff;
    }
    return Math.abs(hash).toString(16).padStart(30, '0').substring(0, 30);
}
                `
            }
        ];
        
        algorithms.forEach((alg, i) => {
            console.log(`\n${alg.name}:`);
            console.log(`   描述: ${alg.description}`);
            console.log(`   代码: ${alg.code.trim()}`);
        });
        
        return algorithms;
    }

    // 运行完整分析
    runFullExtraction() {
        console.log('🚀 开始Token算法提取分析\n');
        
        const files = ['commons.js', 'app.js', 'c.js', 'v.js'];
        
        files.forEach(filename => {
            if (fs.existsSync(filename)) {
                console.log(`\n${'='.repeat(50)}`);
                console.log(`📁 分析文件: ${filename}`);
                console.log(`${'='.repeat(50)}`);
                
                const content = fs.readFileSync(filename, 'utf8');
                
                this.extractTokenFunctions(filename, content);
                this.analyzeWxLoginFlow(filename, content);
                this.findApiCallPatterns(filename, content);
                this.analyzeTokenUpdate(filename, content);
                this.findCryptoAlgorithms(filename, content);
                this.extractConstants(filename, content);
            }
        });
        
        // 综合分析
        this.analyzeTokenGeneration();
        const algorithms = this.generatePossibleAlgorithms();
        
        // 保存结果
        const report = {
            findings: this.findings,
            authFlows: this.authFlows,
            algorithms: algorithms,
            timestamp: new Date().toISOString()
        };
        
        fs.writeFileSync('token_algorithm_analysis.json', JSON.stringify(report, null, 2));
        console.log('\n📄 详细分析报告已保存到: token_algorithm_analysis.json');
        
        console.log('\n🎯 总结建议:');
        console.log('1. 重点关注微信登录code的处理流程');
        console.log('2. 分析getPluginAuthSessionKey API的调用参数');
        console.log('3. 查找token更新时的具体赋值逻辑');
        console.log('4. 测试生成的算法与真实token的匹配度');
        
        return report;
    }
}

// 运行提取分析
if (require.main === module) {
    const extractor = new TokenAlgorithmExtractor();
    extractor.runFullExtraction();
}

module.exports = TokenAlgorithmExtractor;
