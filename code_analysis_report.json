[{"file": "c.js", "type": "token", "pattern": "/accessToken[^,;]{0,100}/g", "matches": ["accessToken:\"\"", "accessToken=e.accessToken", "accessToken||\"\")"]}, {"file": "c.js", "type": "token", "pattern": "/sessionId[^,;]{0,100}/g", "matches": ["sessionId:n}=r.data||{}", "sessionId:n", "sessionId", "sessionId:o})", "sessionId:n}=e"]}, {"file": "c.js", "type": "token", "pattern": "/hasToken[^,;]{0,100}/g", "matches": ["hasToken:!1}", "hasToken=!0", "hasToken&&e.requestTime<T.tokenTime)return $(e._options.config", "hasToken&&(n.A.trigger(\"auth:login\")", "hasToken=!1"]}, {"file": "c.js", "type": "token", "pattern": "/tokenTime[^,;]{0,100}/g", "matches": ["tokenTime=Date.now()", "tokenTime)return $(e._options.config"]}, {"file": "c.js", "type": "token", "pattern": "/getToken[^}]{0,200}/g", "matches": ["getToken\",data:{env:t,appName:e,approveScene:0", "getToken()||{", "getToken()||{"]}, {"file": "c.js", "type": "auth", "pattern": "/login[^}]{0,150}/g", "matches": ["login\",(function(){return e()", "login/auth.json\",{method:\"POST\",data:e", "login-dialog/sms.json\",{method:\"POST\",data:e"]}, {"file": "c.js", "type": "auth", "pattern": "/auth[^}]{0,150}/g", "matches": ["auth\",1],[NaN,\"host\",void 0,1,1],[/:(\\d*)$/,\"port\",void 0,1],[NaN,\"hostname\",void 0,1,1]],h={hash:1,query:1", "auth&&(~(h=P.auth.indexOf(\":\"))?(P.username=P.auth.slice(0,h),P.username=encodeURIComponent(decodeURIComponent(P.username)),P.password=P.auth.slice(h+1),P", "auth)),P.auth=P.password?P.username+\":\"+P.password:P.username),P.origin=\"file:\"!==P.protocol&&f(P.protocol)&&P.host?P.protocol+\"//\"+P.host:\"null\",P.href=P"]}, {"file": "c.js", "type": "auth", "pattern": "/wscuser[^\"']{0,100}/g", "matches": ["wscuser/levelcenter/fill?alias=", "wscuser/membercenter/"]}, {"file": "c.js", "type": "auth", "pattern": "/Authorization[^,;]{0,100}/g", "matches": ["Authorization:wx.getStorageSync(\"token\")"]}, {"file": "c.js", "type": "crypto", "pattern": "/sha[^}]{0,100}/gi", "matches": ["ShareInfo\",\"showShareMenu\",\"updateShareMenu\",\"hideShareMenu\",\"chooseLocation\",\"navigateToMiniProgram\",\"", "shareFileMessage\",\"shareVideoMessage\",\"showShareImageMenu\",\"getPlatformSDK\",\"getPageStackSync\",\"createI", "Share\",\"disableShare\"],re={getEnv:o.default.getEnv,requestPayment:(U=o.default.$native.requestPayment,B"]}, {"file": "c.js", "type": "crypto", "pattern": "/encrypt[^}]{0,100}/gi", "matches": ["encryptionKey:n", "encryptionKey:o", "encryptionKey:o"]}, {"file": "c.js", "type": "crypto", "pattern": "/hash[^}]{0,100}/gi", "matches": ["hash\"],[\"?\",\"query\"],function(e,t){return f(t.protocol)?e.replace(/\\\\/g,\"/\"):e", "hash:1,query:1", "hash\":if(t){var i=\"pathname\"===e?\"/\":\"#\";n[e]=t.charAt(0)!==i?i+t:t"]}, {"file": "c.js", "type": "crypto", "pattern": "/crypto[^}]{0,100}/gi", "matches": ["CryptoInfo()||{", "CryptoInfo:()=>Q,getHQKdtId:()=>V,getHiddenPowerBy:()=>U,getKdtId:()=>T,getLocationAuth:()=>A.u5,getNavCon", "crypto_info:I={"]}, {"file": "c.js", "type": "crypto", "pattern": "/sign[^}]{0,100}/gi", "matches": ["sign)return!1;var e=new String(\"abc\");if(e[5]=\"de\",\"5\"===Object.getOwnPropertyNames(e)[0])return!1;for(v", "sign({", "sign:function(e,o){for(var n,i,s=function(e){if(null==e)throw new TypeError(\"Object.assign cannot be cal"]}, {"file": "c.js", "type": "api", "pattern": "/youzan\\.com[^\"']{0,100}/g", "matches": ["youzan.com/h5-extension-service/gateway/getToken", "youzan.com", "youzan.com", "youzan.com", "youzan.com/.test(r)||/shop[0-9]{6}[0-9]*(-[0-9]{2}){0,1}\\.youzan\\.com/.test(r))?r:r&&/shop[0-9]{6}[0-9]*(-[0-9"]}, {"file": "c.js", "type": "api", "pattern": "/\\.json[^\"']{0,50}/g", "matches": [".json", ".json", ".json", ".json", ".json"]}, {"file": "c.js", "type": "api", "pattern": "/Extra-Data[^}]{0,100}/g", "matches": ["Extra-Data\"]=JSON.stringify(P),(0,c.Ay)({origin:h,pathname:i,query:n", "Extra-Data\"]||\"{", "Extra-Data\"]=JSON.stringify(t),!0", "Extra-Data\":JSON.stringify({is_weapp:1,client:\"weapp\",bizEnv:\"wsc\",skip_sid:1,version:(0,o.r)(\"userVersion\")"]}, {"file": "v.js", "type": "token", "pattern": "/accessToken[^,;]{0,100}/g", "matches": ["accessToken:\"\"", "accessToken:e.accessToken", "accessToken||\"\""]}, {"file": "v.js", "type": "token", "pattern": "/sessionId[^,;]{0,100}/g", "matches": ["sessionId:\"\"", "sessionId:e.sessionId})}", "sessionId"]}, {"file": "v.js", "type": "token", "pattern": "/tokenTime[^,;]{0,100}/g", "matches": ["tokenTime:0", "tokenTime:Date.now()"]}, {"file": "v.js", "type": "token", "pattern": "/updateToken[^}]{0,200}/g", "matches": ["updateToken:()=>T"]}, {"file": "v.js", "type": "auth", "pattern": "/login[^}]{0,150}/g", "matches": ["login()", "login_ticket_info\",l=\"CACHE_ON_PRIVACY_AUTH\",f=\"CACHE_NEED_PRIVACY_AUTH\",d=\"CACHE_REQ_HOOK_INIT\",h=\"user-auth:sync-state\",p=\"user-auth:auth-success\",v=\"use", "login-before\",g=\"passport-tee-login-before-token\",E=\"passport-tee-login-code-success\",y=\"passport-tee-login-end\";!function(e){e[e.Unknown=-1]=\"Unknown\",e[e"]}, {"file": "v.js", "type": "auth", "pattern": "/auth[^}]{0,150}/g", "matches": ["authLogger:a", "auth_yz\",e.YZUserInfo=\"info_yz\",e.YZExternal=\"external_yz\",e.NativeMobile=\"mobile_n\",e.NativeNickAvatar=\"nick_n\"", "auth:sync-state\",p=\"user-auth:auth-success\",v=\"user-auth:request-auth\",_=\"passport-tee-login-before\",g=\"passport-tee-login-before-token\",E=\"passport-tee-l"]}, {"file": "v.js", "type": "auth", "pattern": "/Authorization[^,;]{0,100}/g", "matches": ["Authorization)}", "Authorization\")&&a.default.$native.onNeedPrivacyAuthorization(((t"]}, {"file": "v.js", "type": "crypto", "pattern": "/sha[^}]{0,100}/gi", "matches": ["shArray:[]", "shArray:t,paraArray:r=\"\"===r[1]?[r[0]]:r", "shArray,c=s.paraArray;if(!c)return e;if(c[1]){var l=n.__assign(n.__assign({"]}, {"file": "v.js", "type": "crypto", "pattern": "/encrypt[^}]{0,100}/gi", "matches": ["encryptedData),i=/deny|cancel|未绑定手机|not authorize|小程序获取权限失败/i.test(r.errMsg||\"\");if((0,f.hE)({path:\"/passpo", "encryptedData=void 0,e.iv=void 0,t(e)"]}, {"file": "v.js", "type": "crypto", "pattern": "/hash[^}]{0,100}/gi", "matches": ["hashArray:[]", "hashArray:t,paraArray:r=\"\"===r[1]?[r[0]]:r", "hashArray,c=s.paraArray;if(!c)return e;if(c[1]){var l=n.__assign(n.__assign({"]}, {"file": "v.js", "type": "crypto", "pattern": "/crypto[^}]{0,100}/gi", "matches": ["CRYPTO_INFO\",c=\"login_ticket_info\",l=\"CACHE_ON_PRIVACY_AUTH\",f=\"CACHE_NEED_PRIVACY_AUTH\",d=\"CACHE_REQ_HOOK", "CRYPTO_INFO:()=>s.Dp,CACHE_LOGIN_TICKET_INFO:()=>s.DZ,InvokeProtocolEvent:()=>a.InvokeProtocolEvent,Mobile", "CRYPTO_INFO\")&&r.d(t,{CACHE_BEHAVIOR_CRYPTO_INFO:function(){return a.CACHE_BEHAVIOR_CRYPTO_INFO"]}, {"file": "v.js", "type": "crypto", "pattern": "/sign[^}]{0,100}/gi", "matches": ["sign:()=>l,default:()=>P,defaultI18n:()=>p,format:()=>C,parse:()=>N,setGlobalDateI18n:()=>_,setGlobalDat", "sign({", "sign(Object.assign({"]}, {"file": "v.js", "type": "api", "pattern": "/youzan\\.com[^\"']{0,100}/g", "matches": ["youzan.com/wscshop/common/error/not-exist", "youzan.com", "youzan.com", "youzan.com", "youzan.com"]}, {"file": "v.js", "type": "api", "pattern": "/\\.json[^\"']{0,50}/g", "matches": [".json", ".json", ".json", ".json", ".json"]}, {"file": "v.js", "type": "api", "pattern": "/request[^}]{0,150}/g", "matches": ["request options.url accept a string\"),e.method=e.method?e.method.toUpperCase():\"GET\",e.data=(0,o.o)(e.data),a.A.emit(\"requestStart\",e),i.P.request(e).then((f", "requestComplete\",t,e),a.A.emit(\"requestSuccess\",t,e),t", "requestComplete\",t,e),a.A.emit(\"requestFail\",t,e),Promise.reject(t)", "requestStart\",\"requestComplete\",\"requestSuccess\",\"requestFail\"],a=function(e,t){var r=e[t];if(\"function\"==typeof r){e[t]=function(){for(var n=arguments.lengt", "request\",\"getStorage\",\"setStorage\",\"getExtConfig\",\"openAddress\"];var ee=function(e,t){void 0===t&&(t=!1);var r={"]}, {"file": "v.js", "type": "api", "pattern": "/Extra-Data[^}]{0,100}/g", "matches": ["Extra-Data\"]||\"{", "Extra-Data\":JSON.stringify(g)", "Extra-Data\"]=JSON.stringify(f),!0"]}, {"file": "app.js", "type": "token", "pattern": "/accessToken[^,;]{0,100}/g", "matches": ["accessToken\")||null}function fs(){return ls(\"fansType\")||1343}function gs(){return ls(\"sessionId\")||null}functi"]}, {"file": "app.js", "type": "token", "pattern": "/sessionId[^,;]{0,100}/g", "matches": ["sessionId", "sessionId\")||null}function ms(){return ls(\"buyerId\")||null}function vs(){return ls(\"mobile\")||null}function y", "sessionId:o}=e.userTokenInfo", "sessionId:r}=e.userTokenInfo", "sessionId\""]}, {"file": "app.js", "type": "token", "pattern": "/hasToken[^,;]{0,100}/g", "matches": ["hasToken:()=>ps", "hasToken()&&(e.getKdtId()||!e.needUpdateKdtIdByServer()))for(var t=null", "hasToken()&&wx.showToast({title:\"登录成功\"})}))}})})", "hasToken:!1"]}, {"file": "app.js", "type": "token", "pattern": "/getToken[^}]{0,200}/g", "matches": ["getToken:()=>ls,getUserInfo:()=>_s,getUserInfoSync:()=>ys,hasToken:()=>ps,login:()=>ds,onNeedPrivacyAuth:()=>Bi,prefetchUserAuthData:()=>zi,refreshUserAuthData:()=>Mi,resolveProtocol:()=>Ti,resolveTeeAPI:()=>", "getToken\",header:{\"content-type\":\"application/json\""]}, {"file": "app.js", "type": "auth", "pattern": "/login[^}]{0,150}/g", "matches": ["login\",l:\"login;forceLogin;hasLogin;getUserInfo;getAppId;getAuthType;getPlatform;getHostApp\",lc:\"login;forceLogin;getUserInfo\",li:\"onAppShow;onAppLaunch\"", "login:()=>Ki,updateLoginStorage:()=>Yi", "login:()=>ds,onNeedPrivacyAuth:()=>Bi,prefetchUserAuthData:()=>zi,refreshUserAuthData:()=>Mi,resolveProtocol:()=>Ti,resolveTeeAPI:()=>ji,syncAuthState:()=>"]}, {"file": "app.js", "type": "auth", "pattern": "/auth[^}]{0,150}/g", "matches": ["authTypeList:t.authTypeList", "authList:t.authTypeList,state:t.auth<PERSON><PERSON>,residueAuthList:t.leftAuthTypeList,pageUrl:t.pageUrl", "authTypeList:[r.AuthType.PROTOCOL],actionType:r.AuthActionType.Agree"]}, {"file": "app.js", "type": "auth", "pattern": "/wscuser[^\"']{0,100}/g", "matches": ["wscuser/weapp/create-client.json", "wscuser/membercenter/pointsName.json", "wscuser/weapp/create-client.json"]}, {"file": "app.js", "type": "crypto", "pattern": "/sha[^}]{0,100}/gi", "matches": ["ShareTimeline\",\"onShareAppMessage\"],P=(0,p.__spreadArray)([\"onPageScroll\",\"onReachBottom\",\"onPullDownRe", "ShareAppMessage:function(e){return this.runApi(\"onShareAppMessage\",e)", "ShareTimeline:function(){return this.runApi(\"onShareTimeline\")"]}, {"file": "app.js", "type": "crypto", "pattern": "/encrypt[^}]{0,100}/gi", "matches": ["encryptedData:t,iv:n", "encryptedData=t,u.iv=n,this.request({path:\"/wa/api/common/generate-chat-id\",data:u"]}, {"file": "app.js", "type": "crypto", "pattern": "/sign[^}]{0,100}/gi", "matches": ["sign({", "sign(e,{name:\"RantaRuntimeError\",metaData:{ranta:t", "sign)({"]}, {"file": "app.js", "type": "api", "pattern": "/youzan\\.com[^\"']{0,100}/g", "matches": ["youzan.com/v3/weapp/log", "youzan.com/oauth/getToken", "youzan.com/wscgoods/tee-app/detail-v2.json", "youzan.com"]}, {"file": "app.js", "type": "api", "pattern": "/\\.json[^\"']{0,50}/g", "matches": [".json", ".json", ".json", ".json", ".json"]}, {"file": "app.js", "type": "api", "pattern": "/request[^}]{0,150}/g", "matches": ["requestUseCdn)({path:\"/wscump/member-config.json\"", "requestProtocolAuth\",\"method\",{allowMultiple:!0", "requestProtocolAuth(e){return this.invokeProtocol().then((t=>{var{needSkipSigned:n", "requestProtocolAuth\",[yo],Object.getOwnPropertyDescriptor(Io.prototype,\"requestProtocolAuth\"),Io.prototype),(0,Tn.A)(Io.prototype,\"hideProtocolAuth\",[bo],Obj", "requestV2)({origin:\"cashier\",withCredentials:!0,path:\"/wsctrade/multistore/selfFetchPoint/getCity.json\",method:\"GET\",data:{lat:t,lon:n"]}, {"file": "app.js", "type": "api", "pattern": "/Extra-Data[^}]{0,100}/g", "matches": ["Extra-Data\":JSON.stringify(t)"]}, {"file": "commons.js", "type": "token", "pattern": "/accessToken[^,;]{0,100}/g", "matches": ["accessToken", "accessToken:p})", "accessToken:e.accessToken||\"\"}}function p(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0", "accessToken:\"\"", "accessToken=e.accessToken"]}, {"file": "commons.js", "type": "token", "pattern": "/sessionId[^,;]{0,100}/g", "matches": ["sessionId", "sessionId:d", "sessionId||\"\"}", "sessionId:e.sessionId||\"\"", "sessionId:\"\""]}, {"file": "commons.js", "type": "token", "pattern": "/hasToken[^,;]{0,100}/g", "matches": ["hasToken:!1}", "hasToken=!0", "hasToken&&o.requestTime<y.tokenTime)return q(o._options.config", "hasToken&&(a.a.trigger(\"auth:login\")", "hasToken=!1"]}, {"file": "commons.js", "type": "token", "pattern": "/tokenTime[^,;]{0,100}/g", "matches": ["tokenTime=Date.now()", "tokenTime)return q(o._options.config"]}, {"file": "commons.js", "type": "token", "pattern": "/updateToken[^}]{0,200}/g", "matches": ["updateToken({sessionId:d,accessToken:p", "updateToken\",(function(){return f", "updateToken\",(function(){return I", "updateToken)({accessToken:e.accessToken,session_id:e.sessionId", "updateToken)({accessToken:e.accessToken,sessionId:e.sessionId"]}, {"file": "commons.js", "type": "auth", "pattern": "/getPluginAuthSessionKey[^}]{0,300}/g", "matches": ["getPluginAuthSessionKey.json\",method:\"post\",data:o"]}, {"file": "commons.js", "type": "auth", "pattern": "/login[^}]{0,150}/g", "matches": ["login\"),y.hasKdtId=!1,y.hasToken=!1,y.hasShop=!1),q(o._options.config,i)", "login\",(function(){return d(!0)"]}, {"file": "commons.js", "type": "auth", "pattern": "/auth[^}]{0,150}/g", "matches": ["authentry/\"+e.api).replace(\"//\",\"/\"),T(e)", "auth:token:fail\",u);var r=z(o=P(o._options));e(r(n,t))", "auth:token:fail\",u),y.hasToken&&o.requestTime<y.tokenTime)return q(o._options.config,i);y.hasToken&&(a.a.trigger(\"auth:login\"),y.hasKdtId=!1,y.hasToken=!1"]}, {"file": "commons.js", "type": "auth", "pattern": "/wscuser[^\"']{0,100}/g", "matches": ["wscuser/wx/getPluginAuthSessionKey.json"]}, {"file": "commons.js", "type": "crypto", "pattern": "/sha[^}]{0,100}/gi", "matches": ["shared-key.js\")(\"IE_PROTO\");e.exports=function(e,o){var n,a=r(e),i=0,c=[];for(n in a)n!=u&&t(a,n)&&c.pu", "shared-key.js\":function(e,o,n){var t=n(\"./node_modules/core-js/library/modules/_shared.js\")(\"keys\"),r=n", "shared.js\":function(e,o,n){var t=n(\"./node_modules/core-js/library/modules/_core.js\"),r=n(\"./node_modul"]}, {"file": "commons.js", "type": "crypto", "pattern": "/sign[^}]{0,100}/gi", "matches": ["sign({", "sign.js\":function(e,o,n){e.exports={default:n(\"./node_modules/core-js/library/fn/object/assign.js\"),__es", "sign.js\"),s=(t=r)&&t.__esModule?t:{default:t"]}, {"file": "commons.js", "type": "api", "pattern": "/youzan\\.com[^\"']{0,100}/g", "matches": ["youzan.com", "youzan.com", "youzan.com", "youzan.com", "youzan.com"]}, {"file": "commons.js", "type": "api", "pattern": "/\\.json[^\"']{0,50}/g", "matches": [".json", ".json", ".json", ".json"]}, {"file": "commons.js", "type": "api", "pattern": "/request[^}]{0,150}/g", "matches": ["request/base.js\":function(e,o,n){n.r(o),n.d(o,\"default\",(function(){return i", "request/cache.js\"),a={method:\"GET\",header:{\"content-type\":\"application/x-www-form-urlencoded\"", "request/cache.js\":function(e,o,n){n.r(o);var t=n(\"./node_modules/babel-runtime/helpers/extends.js\"),r=n.n(t),s=n(\"./node_modules/@youzan/weapp-utils/lib/debo", "request-cache\",data:e", "request-cache\")||{"]}, {"file": "commons.js", "type": "api", "pattern": "/Extra-Data[^}]{0,100}/g", "matches": ["Extra-Data\"]=JSON.stringify({is_weapp:1,sid:y.sessionId,version:y.version"]}]