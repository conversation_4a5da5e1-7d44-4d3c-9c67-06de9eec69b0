/**
 * 有赞小程序认证机制调试模拟器
 * 用于测试和调试Token生成、API调用等功能
 */

const crypto = require('crypto');
const https = require('https');
const querystring = require('querystring');

class YouzanAuthDebugger {
  constructor() {
    this.config = {
      // 模拟的应用配置
      appId: "wx1234567890abcdef",
      pluginId: "wx9e5eba73bf23a27a",
      version: "1.0.0",
      
      // API域名配置
      domains: {
        cashier: "https://cashier.youzan.com",
        uic: "https://uic.youzan.com", 
        carmen: "https://open.youzan.com",
        h5: "https://h5.youzan.com",
        trade: "https://trade.youzan.com",
        qiniu: "https://img.yzcdn.cn",
        money: "https://money.youzan.com"
      }
    };
    
    this.authState = {
      hasToken: false,
      hasKdtId: false,
      hasShop: false,
      tokenTime: 0,
      accessToken: "",
      sessionId: "",
      kdtId: "",
      offlineId: ""
    };
    
    this.requestQueue = [];
    this.debugLogs = [];
  }

  /**
   * 生成模拟的Token数据
   */
  generateMockToken(params) {
    const timestamp = Date.now();
    const randomStr = crypto.randomBytes(16).toString('hex');
    
    // 模拟Token生成算法
    const tokenData = {
      accessToken: this.generateAccessToken(params, timestamp, randomStr),
      sessionId: this.generateSessionId(params, timestamp),
      kdtId: params.kdtId || "12345678",
      expire: timestamp + 24 * 3600 * 1000 // 24小时后过期
    };
    
    this.log('Token生成', tokenData);
    return tokenData;
  }

  /**
   * 生成Access Token
   */
  generateAccessToken(params, timestamp, randomStr) {
    // 模拟有赞的Token生成逻辑
    const payload = `${params.appId}_${params.openId}_${timestamp}_${randomStr}`;
    const hash = crypto.createHash('sha256').update(payload).digest('hex');
    return `yz_${hash.substring(0, 32)}`;
  }

  /**
   * 生成Session ID
   */
  generateSessionId(params, timestamp) {
    const sessionData = `${params.openId}_${timestamp}`;
    const hash = crypto.createHash('md5').update(sessionData).digest('hex');
    return `sess_${hash}`;
  }

  /**
   * 模拟Token获取API调用
   */
  async mockGetToken(params) {
    this.log('开始获取Token', params);
    
    // 验证必需参数
    const requiredParams = ['appId', 'openId', 'shopId', 'kdtId'];
    for (const param of requiredParams) {
      if (!params[param]) {
        throw new Error(`缺少必需参数: ${param}`);
      }
    }
    
    // 模拟网络延迟
    await this.sleep(Math.random() * 1000 + 500);
    
    // 模拟成功率（90%成功）
    if (Math.random() < 0.1) {
      throw new Error('网络错误或服务器异常');
    }
    
    const tokenData = this.generateMockToken(params);
    
    // 更新认证状态
    this.updateAuthState({
      hasToken: true,
      hasKdtId: true,
      hasShop: true,
      tokenTime: Date.now(),
      ...tokenData
    });
    
    this.log('Token获取成功', tokenData);
    return tokenData;
  }

  /**
   * 模拟API请求
   */
  async mockApiRequest(options) {
    this.log('发起API请求', options);
    
    // 检查Token有效性
    if (!this.authState.hasToken) {
      throw new Error('Token无效，需要重新获取');
    }
    
    // 检查Token是否过期
    if (Date.now() > this.authState.expire) {
      this.log('Token已过期，需要刷新');
      this.authState.hasToken = false;
      throw new Error('Token已过期');
    }
    
    // 构建完整的请求URL
    const fullUrl = this.buildRequestUrl(options);
    const headers = this.buildRequestHeaders(options.header);
    
    this.log('构建请求', { url: fullUrl, headers });
    
    // 模拟网络请求
    await this.sleep(Math.random() * 500 + 200);
    
    // 模拟响应
    const response = this.generateMockResponse(options);
    
    this.log('收到响应', response);
    return response;
  }

  /**
   * 构建请求URL
   */
  buildRequestUrl(options) {
    const { origin, path, query = {} } = options;
    const baseUrl = this.config.domains[origin];
    
    if (!baseUrl) {
      throw new Error(`未知的API域名: ${origin}`);
    }
    
    // 自动注入认证参数
    if (!options.config?.noQuery) {
      query.app_id = this.config.appId;
      query.kdt_id = query.kdt_id || this.authState.kdtId;
      query.access_token = this.authState.accessToken;
      
      if (!options.config?.noStoreId) {
        query.store_id = this.authState.offlineId || "";
      }
    }
    
    const queryString = querystring.stringify(query);
    return `${baseUrl}${path}${queryString ? '?' + queryString : ''}`;
  }

  /**
   * 构建请求头
   */
  buildRequestHeaders(customHeaders = {}) {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': 'YouzanMiniProgram/1.0.0',
      ...customHeaders
    };
    
    // 添加Extra-Data头
    headers['Extra-Data'] = JSON.stringify({
      is_weapp: 1,
      sid: this.authState.sessionId,
      version: this.config.version
    });
    
    return headers;
  }

  /**
   * 生成模拟响应
   */
  generateMockResponse(options) {
    const { path } = options;
    
    // 根据不同的API路径生成不同的模拟数据
    if (path.includes('address/getAddressList')) {
      return {
        code: 0,
        msg: 'success',
        data: [
          {
            id: 1,
            name: '张三',
            phone: '13800138000',
            province: '北京市',
            city: '北京市',
            district: '朝阳区',
            detail: '三里屯街道1号'
          }
        ]
      };
    } else if (path.includes('goods/detail')) {
      return {
        code: 0,
        msg: 'success',
        data: {
          goods_id: 123456,
          title: '测试商品',
          price: 9900, // 分为单位
          stock: 100,
          images: ['https://img.yzcdn.cn/test.jpg']
        }
      };
    } else if (path.includes('cart/add')) {
      return {
        code: 0,
        msg: 'success',
        data: {
          cart_id: Date.now(),
          message: '添加成功'
        }
      };
    }
    
    // 默认响应
    return {
      code: 0,
      msg: 'success',
      data: {
        timestamp: Date.now(),
        message: '模拟响应数据'
      }
    };
  }

  /**
   * 更新认证状态
   */
  updateAuthState(newState) {
    Object.assign(this.authState, newState);
    this.log('认证状态更新', this.authState);
  }

  /**
   * 记录调试日志
   */
  log(action, data) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      action,
      data: JSON.parse(JSON.stringify(data))
    };
    
    this.debugLogs.push(logEntry);
    console.log(`[${logEntry.timestamp}] ${action}:`, data);
  }

  /**
   * 获取调试日志
   */
  getDebugLogs() {
    return this.debugLogs;
  }

  /**
   * 清空调试日志
   */
  clearDebugLogs() {
    this.debugLogs = [];
  }

  /**
   * 模拟延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 导出调试报告
   */
  exportDebugReport() {
    return {
      config: this.config,
      authState: this.authState,
      logs: this.debugLogs,
      summary: {
        totalRequests: this.debugLogs.filter(log => log.action.includes('请求')).length,
        tokenRefreshCount: this.debugLogs.filter(log => log.action.includes('Token')).length,
        errors: this.debugLogs.filter(log => log.data && log.data.error).length
      }
    };
  }
}

module.exports = YouzanAuthDebugger;
