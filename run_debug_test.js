/**
 * 有赞小程序认证机制调试测试脚本
 * 运行各种测试场景来验证认证流程
 */

const YouzanAuthDebugger = require('./debug_simulator.js');

class DebugTestRunner {
  constructor() {
    this.debugger = new YouzanAuthDebugger();
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行有赞小程序认证机制调试测试\n');
    
    try {
      await this.testTokenGeneration();
      await this.testApiRequests();
      await this.testTokenExpiry();
      await this.testErrorHandling();
      await this.testConcurrentRequests();
      
      this.printTestSummary();
      this.exportResults();
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
    }
  }

  /**
   * 测试Token生成
   */
  async testTokenGeneration() {
    console.log('📝 测试1: Token生成机制');
    
    const testParams = {
      appId: "wx1234567890abcdef",
      openId: "oUser123456789",
      shopId: "shop_12345",
      kdtId: "87654321",
      pluginId: "wx9e5eba73bf23a27a"
    };
    
    try {
      const tokenData = await this.debugger.mockGetToken(testParams);
      
      // 验证Token格式
      const isValidToken = tokenData.accessToken.startsWith('yz_') && 
                          tokenData.accessToken.length > 30;
      const isValidSession = tokenData.sessionId.startsWith('sess_');
      
      this.recordTestResult('Token生成', true, {
        tokenValid: isValidToken,
        sessionValid: isValidSession,
        tokenData
      });
      
      console.log('✅ Token生成测试通过');
      console.log(`   Access Token: ${tokenData.accessToken}`);
      console.log(`   Session ID: ${tokenData.sessionId}`);
      console.log(`   KDT ID: ${tokenData.kdtId}\n`);
      
    } catch (error) {
      this.recordTestResult('Token生成', false, { error: error.message });
      console.log('❌ Token生成测试失败:', error.message);
    }
  }

  /**
   * 测试API请求
   */
  async testApiRequests() {
    console.log('📝 测试2: API请求机制');
    
    const testCases = [
      {
        name: '获取地址列表',
        options: {
          origin: 'cashier',
          method: 'POST',
          path: '/wsctrade/uic/address/getAddressList.json'
        }
      },
      {
        name: '获取商品详情',
        options: {
          origin: 'h5',
          method: 'GET',
          path: '/wscgoods/detail/getGoodsInfo.json',
          query: { goods_id: '123456' }
        }
      },
      {
        name: '添加购物车',
        options: {
          origin: 'trade',
          method: 'POST',
          path: '/wsctrade/cart/add.json',
          data: { goods_id: '123456', sku_id: '789', quantity: 1 }
        }
      }
    ];
    
    for (const testCase of testCases) {
      try {
        const response = await this.debugger.mockApiRequest(testCase.options);
        
        const isSuccess = response.code === 0;
        this.recordTestResult(`API请求-${testCase.name}`, isSuccess, response);
        
        if (isSuccess) {
          console.log(`✅ ${testCase.name} 请求成功`);
          console.log(`   响应: ${JSON.stringify(response.data).substring(0, 100)}...`);
        } else {
          console.log(`❌ ${testCase.name} 请求失败: ${response.msg}`);
        }
        
      } catch (error) {
        this.recordTestResult(`API请求-${testCase.name}`, false, { error: error.message });
        console.log(`❌ ${testCase.name} 请求异常:`, error.message);
      }
    }
    console.log();
  }

  /**
   * 测试Token过期处理
   */
  async testTokenExpiry() {
    console.log('📝 测试3: Token过期处理');
    
    try {
      // 手动设置Token为过期状态
      this.debugger.authState.expire = Date.now() - 1000;
      
      const response = await this.debugger.mockApiRequest({
        origin: 'h5',
        path: '/test/expired.json'
      });
      
      // 如果没有抛出异常，说明过期检测有问题
      this.recordTestResult('Token过期检测', false, { 
        error: '过期Token未被正确检测' 
      });
      console.log('❌ Token过期检测失败');
      
    } catch (error) {
      if (error.message.includes('过期')) {
        this.recordTestResult('Token过期检测', true, { 
          message: '正确检测到Token过期' 
        });
        console.log('✅ Token过期检测正常');
      } else {
        this.recordTestResult('Token过期检测', false, { error: error.message });
        console.log('❌ Token过期检测异常:', error.message);
      }
    }
    console.log();
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log('📝 测试4: 错误处理机制');
    
    const errorTests = [
      {
        name: '缺少必需参数',
        test: async () => {
          await this.debugger.mockGetToken({ appId: 'test' }); // 缺少其他参数
        }
      },
      {
        name: '无效域名',
        test: async () => {
          await this.debugger.mockApiRequest({
            origin: 'invalid_domain',
            path: '/test.json'
          });
        }
      },
      {
        name: '无Token状态请求',
        test: async () => {
          this.debugger.authState.hasToken = false;
          await this.debugger.mockApiRequest({
            origin: 'h5',
            path: '/test.json'
          });
        }
      }
    ];
    
    for (const errorTest of errorTests) {
      try {
        await errorTest.test();
        this.recordTestResult(`错误处理-${errorTest.name}`, false, {
          error: '应该抛出异常但没有'
        });
        console.log(`❌ ${errorTest.name}: 错误处理失败`);
      } catch (error) {
        this.recordTestResult(`错误处理-${errorTest.name}`, true, {
          message: `正确捕获错误: ${error.message}`
        });
        console.log(`✅ ${errorTest.name}: 错误处理正常`);
      }
    }
    console.log();
  }

  /**
   * 测试并发请求
   */
  async testConcurrentRequests() {
    console.log('📝 测试5: 并发请求处理');
    
    // 重新获取有效Token
    await this.debugger.mockGetToken({
      appId: "wx1234567890abcdef",
      openId: "oUser123456789", 
      shopId: "shop_12345",
      kdtId: "87654321"
    });
    
    try {
      const concurrentRequests = Array.from({ length: 5 }, (_, i) => 
        this.debugger.mockApiRequest({
          origin: 'h5',
          path: `/test/concurrent_${i}.json`,
          query: { request_id: i }
        })
      );
      
      const results = await Promise.allSettled(concurrentRequests);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      
      this.recordTestResult('并发请求', true, {
        totalRequests: 5,
        successCount,
        failureCount: 5 - successCount
      });
      
      console.log(`✅ 并发请求测试完成: ${successCount}/5 成功`);
      
    } catch (error) {
      this.recordTestResult('并发请求', false, { error: error.message });
      console.log('❌ 并发请求测试失败:', error.message);
    }
    console.log();
  }

  /**
   * 记录测试结果
   */
  recordTestResult(testName, success, details) {
    this.testResults.push({
      testName,
      success,
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 打印测试摘要
   */
  printTestSummary() {
    console.log('📊 测试摘要报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log();
    
    // 显示失败的测试
    if (failedTests > 0) {
      console.log('失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  ❌ ${r.testName}: ${r.details.error || '未知错误'}`);
        });
      console.log();
    }
  }

  /**
   * 导出测试结果
   */
  exportResults() {
    const report = {
      testSummary: {
        totalTests: this.testResults.length,
        passedTests: this.testResults.filter(r => r.success).length,
        failedTests: this.testResults.filter(r => !r.success).length,
        timestamp: new Date().toISOString()
      },
      testResults: this.testResults,
      debugLogs: this.debugger.getDebugLogs(),
      authState: this.debugger.authState
    };
    
    // 保存到文件
    const fs = require('fs');
    fs.writeFileSync('debug_test_report.json', JSON.stringify(report, null, 2));
    console.log('📄 测试报告已保存到: debug_test_report.json');
  }
}

// 运行测试
if (require.main === module) {
  const runner = new DebugTestRunner();
  runner.runAllTests().catch(console.error);
}

module.exports = DebugTestRunner;
